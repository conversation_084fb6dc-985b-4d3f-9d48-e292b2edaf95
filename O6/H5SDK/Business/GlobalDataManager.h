//
//  GlobalDataManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^DataChangeHandler)(NSString *dataType, id _Nullable newData);

@interface GlobalDataManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 数据变化通知
@property (nonatomic, copy, nullable) DataChangeHandler dataChangeHandler;

// AppConfig数据管理
@property (nonatomic, strong, nullable) NSDictionary *appConfigData;
- (void)setAppConfigData:(nullable NSDictionary *)configData;
- (nullable id)getAppConfigValueForKey:(NSString *)key;
- (nullable NSString *)getAppConfigString:(NSString *)key;
- (BOOL)getAppConfigBool:(NSString *)key defaultValue:(BOOL)defaultValue;

// AppConfig Items配置管理
- (nullable NSArray *)getAppConfigItems;
- (nullable NSDictionary *)getAppConfigItemByName:(NSString *)name;
- (nullable id)getAppConfigItemDataByName:(NSString *)name;
- (nullable id)getAppConfigItemDataByName:(NSString *)name defaultValue:(nullable id)defaultValue;

// 认证数据管理
@property (nonatomic, strong, nullable) NSDictionary *authData;
@property (nonatomic, strong, nullable) NSString *authToken;
@property (nonatomic, strong, nullable) NSString *userId;
@property (nonatomic, strong, nullable) NSDictionary *userInfo;
- (void)setAuthData:(nullable NSDictionary *)authData;
- (BOOL)isLoggedIn;
- (void)clearAuthData;

// 策略数据管理
@property (nonatomic, strong, nullable) NSDictionary *strategyData;
- (void)setStrategyData:(nullable NSDictionary *)strategyData;
- (nullable id)getStrategyValueForKey:(NSString *)key;
- (nullable NSString *)getStrategyString:(NSString *)key;
- (BOOL)getStrategyBool:(NSString *)key defaultValue:(BOOL)defaultValue;
- (BOOL)isReviewPackage;

// 策略数据嵌套访问方法
- (nullable NSArray *)getStrategyDataAboutK;
- (nullable NSArray *)getStrategyDataAboutList;
- (nullable NSArray *)getStrategyDataAboutKAtIndex:(NSUInteger)index;
- (nullable id)getStrategyNestedValueForKeyPath:(NSString *)keyPath;

// 策略数据便捷访问方法
- (nullable NSString *)getStrategyH5BaseUrl;
- (nullable NSArray *)getStrategyMessageHandlers;
- (nullable NSArray *)getStrategyEventActions;
- (nullable NSArray *)getStrategyEventKeys;
- (nullable NSArray *)getStrategyAppEvents;
- (nullable NSArray *)getStrategyConfigProperties;
- (nullable NSArray *)getStrategyAllKeys;

// 归因数据管理
@property (nonatomic, strong, nullable) NSDictionary *attributionData;
- (void)setAttributionData:(nullable NSDictionary *)attributionData;

// 数据持久化
- (void)saveAllDataToCache;
- (void)loadAllDataFromCache;
- (void)clearAllData;

@end

NS_ASSUME_NONNULL_END
