//
//  GlobalDataManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "GlobalDataManager.h"
#import "UserDefaultsManager.h"
#import "Constants.h"

@interface GlobalDataManager ()

@property (nonatomic, strong) UserDefaultsManager *userDefaultsManager;
@property (nonatomic, strong) dispatch_queue_t dataQueue;

@end

@implementation GlobalDataManager

// 辅助方法：从字典中安全获取字符串
- (nullable NSString *)stringFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSString class]]) {
        return (NSString *)value;
    }
    if ([value isKindOfClass:[NSNumber class]]) {
        return [(NSNumber *)value stringValue];
    }
    return nil;
}

// 辅助方法：从字典中安全获取数字
- (nullable NSNumber *)numberFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSNumber class]]) {
        return (NSNumber *)value;
    }
    if ([value isKindOfClass:[NSString class]]) {
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        return [formatter numberFromString:(NSString *)value];
    }
    return nil;
}

// 辅助方法：从字典中安全获取布尔值
- (BOOL)boolFromDictionary:(NSDictionary *)dict forKey:(NSString *)key defaultValue:(BOOL)defaultValue {
    if (!dict || !key) return defaultValue;
    id value = dict[key];
    if ([value isKindOfClass:[NSNumber class]]) {
        return [(NSNumber *)value boolValue];
    }
    if ([value isKindOfClass:[NSString class]]) {
        NSString *str = [(NSString *)value lowercaseString];
        return [str isEqualToString:@"true"] || [str isEqualToString:@"1"] || [str isEqualToString:@"yes"];
    }
    return defaultValue;
}

// 辅助方法：从字典中安全获取字典
- (nullable NSDictionary *)dictionaryFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSDictionary class]]) {
        return (NSDictionary *)value;
    }
    return nil;
}

// 辅助方法：从字典中安全获取数组
- (nullable NSArray *)arrayFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSArray class]]) {
        return (NSArray *)value;
    }
    return nil;
}

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static GlobalDataManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.userDefaultsManager = [UserDefaultsManager sharedInstance];
        self.dataQueue = dispatch_queue_create([Constants globalDataQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);

        // 启动时加载缓存数据
        [self loadAllDataFromCache];
    }
    return self;
}

#pragma mark - AppConfig数据管理

- (void)setAppConfigData:(nullable NSDictionary *)configData {
    dispatch_async(self.dataQueue, ^{
        self->_appConfigData = configData;

        // 保存到缓存
        [self.userDefaultsManager setAppConfigData:configData];

        // 通知数据变化
        [self notifyDataChange:@"appConfig" newData:configData];
    });
}

- (nullable id)getAppConfigValueForKey:(NSString *)key {
    if (!key || !self.appConfigData) return nil;

    __block id value = nil;
    dispatch_sync(self.dataQueue, ^{
        value = [self stringFromDictionary:self.appConfigData forKey:key];
        if (!value) {
            value = [self numberFromDictionary:self.appConfigData forKey:key];
        }
        if (!value) {
            value = [self dictionaryFromDictionary:self.appConfigData forKey:key];
        }
        if (!value) {
            value = [self arrayFromDictionary:self.appConfigData forKey:key];
        }
    });

    return value;
}

- (nullable NSString *)getAppConfigString:(NSString *)key {
    if (!key || !self.appConfigData) return nil;

    __block NSString *value = nil;
    dispatch_sync(self.dataQueue, ^{
        value = [self stringFromDictionary:self.appConfigData forKey:key];
    });

    return value;
}

- (BOOL)getAppConfigBool:(NSString *)key defaultValue:(BOOL)defaultValue {
    if (!key || !self.appConfigData) return defaultValue;

    __block BOOL value = defaultValue;
    dispatch_sync(self.dataQueue, ^{
        value = [self boolFromDictionary:self.appConfigData forKey:key defaultValue:defaultValue];
    });

    return value;
}

#pragma mark - AppConfig Items配置管理

- (nullable NSArray *)getAppConfigItems {
    if (!self.appConfigData) return nil;

    __block NSArray *items = nil;
    dispatch_sync(self.dataQueue, ^{
        items = [self arrayFromDictionary:self.appConfigData forKey:@"items"];
    });

    return items;
}

- (nullable NSDictionary *)getAppConfigItemByName:(NSString *)name {
    if (!name || !self.appConfigData) return nil;

    NSArray *items = [self getAppConfigItems];
    if (!items) return nil;

    __block NSDictionary *targetItem = nil;
    dispatch_sync(self.dataQueue, ^{
        for (id item in items) {
            if ([item isKindOfClass:[NSDictionary class]]) {
                NSDictionary *itemDict = (NSDictionary *)item;
                NSString *itemName = [self stringFromDictionary:itemDict forKey:@"name"];
                if ([itemName isEqualToString:name]) {
                    targetItem = itemDict;
                    break;
                }
            }
        }
    });

    return targetItem;
}

- (nullable id)getAppConfigItemDataByName:(NSString *)name {
    if (!name) return nil;

    NSDictionary *item = [self getAppConfigItemByName:name];
    if (!item) return nil;

    __block id data = nil;
    dispatch_sync(self.dataQueue, ^{
        // 尝试获取不同类型的data值
        data = [self stringFromDictionary:item forKey:@"data"];
        if (!data) {
            data = [self numberFromDictionary:item forKey:@"data"];
        }
        if (!data) {
            data = [self dictionaryFromDictionary:item forKey:@"data"];
        }
        if (!data) {
            data = [self arrayFromDictionary:item forKey:@"data"];
        }
        // 如果以上都不匹配，直接获取原始值
        if (!data) {
            data = item[@"data"];
        }
    });

    return data;
}

- (nullable id)getAppConfigItemDataByName:(NSString *)name defaultValue:(nullable id)defaultValue {
    if (!name) return defaultValue;

    id data = [self getAppConfigItemDataByName:name];
    return data ?: defaultValue;
}

#pragma mark - 认证数据管理

- (void)setAuthData:(nullable NSDictionary *)authData {
    dispatch_async(self.dataQueue, ^{
        self->_authData = authData;

        if (authData) {
            // 提取token和用户信息
            self->_authToken = [self stringFromDictionary:authData forKey:@"token"];
            self->_userInfo = [self dictionaryFromDictionary:authData forKey:@"userInfo"];
            self->_userId = [self stringFromDictionary:self.userInfo forKey:@"userId"];
        } else {
            self->_authToken = nil;
            self->_userInfo = nil;
            self->_userId = nil;
        }

        // 保存到缓存
        [self.userDefaultsManager setAuthData:authData];

        // 通知数据变化
        [self notifyDataChange:@"authData" newData:authData];
    });
}

- (BOOL)isLoggedIn {
    __block BOOL loggedIn = NO;
    dispatch_sync(self.dataQueue, ^{
        loggedIn = (self.authToken != nil && self.authToken.length > 0);
    });
    return loggedIn;
}

- (void)clearAuthData {
    [self setAuthData:nil];
}

#pragma mark - 策略数据管理

- (void)setStrategyData:(nullable NSDictionary *)strategyData {
    dispatch_async(self.dataQueue, ^{
        self->_strategyData = strategyData;

        // 保存到缓存
        [self.userDefaultsManager setStrategyData:strategyData];

        // 通知数据变化
        [self notifyDataChange:@"strategyData" newData:strategyData];
    });
}

- (nullable id)getStrategyValueForKey:(NSString *)key {
    if (!key || !self.strategyData) return nil;

    __block id value = nil;
    dispatch_sync(self.dataQueue, ^{
        value = [self stringFromDictionary:self.strategyData forKey:key];
        if (!value) {
            value = [self numberFromDictionary:self.strategyData forKey:key];
        }
        if (!value) {
            value = [self dictionaryFromDictionary:self.strategyData forKey:key];
        }
        if (!value) {
            value = [self arrayFromDictionary:self.strategyData forKey:key];
        }
    });

    return value;
}

- (nullable NSString *)getStrategyString:(NSString *)key {
    if (!key || !self.strategyData) return nil;

    __block NSString *value = nil;
    dispatch_sync(self.dataQueue, ^{
        value = [self stringFromDictionary:self.strategyData forKey:key];
    });

    return value;
}

- (BOOL)getStrategyBool:(NSString *)key defaultValue:(BOOL)defaultValue {
    if (!key || !self.strategyData) return defaultValue;

    __block BOOL value = defaultValue;
    dispatch_sync(self.dataQueue, ^{
        value = [self boolFromDictionary:self.strategyData forKey:key defaultValue:defaultValue];
    });

    return value;
}

- (BOOL)isReviewPackage {
    return [self getStrategyBool:@"isReviewPkg" defaultValue:NO];
}

#pragma mark - 策略数据嵌套访问方法

// 获取 data.about.k 数组
- (nullable NSArray *)getStrategyDataAboutK {
    if (!self.strategyData) return nil;

    __block NSArray *kArray = nil;
    dispatch_sync(self.dataQueue, ^{
        // 获取 data 字典
        NSDictionary *dataDict = [self dictionaryFromDictionary:self.strategyData forKey:@"data"];
        if (!dataDict) return;

        // 获取 about 字典
        NSDictionary *aboutDict = [self dictionaryFromDictionary:dataDict forKey:@"about"];
        if (!aboutDict) return;

        // 获取 k 数组
        kArray = [self arrayFromDictionary:aboutDict forKey:@"k"];
    });

    return kArray;
}

// 获取 data.about.list 数组
- (nullable NSArray *)getStrategyDataAboutList {
    if (!self.strategyData) return nil;

    __block NSArray *listArray = nil;
    dispatch_sync(self.dataQueue, ^{
        // 获取 data 字典
        NSDictionary *dataDict = [self dictionaryFromDictionary:self.strategyData forKey:@"data"];
        if (!dataDict) return;

        // 获取 about 字典
        NSDictionary *aboutDict = [self dictionaryFromDictionary:dataDict forKey:@"about"];
        if (!aboutDict) return;

        // 获取 list 数组
        listArray = [self arrayFromDictionary:aboutDict forKey:@"list"];
    });

    return listArray;
}

// 获取 data.about.k 数组中指定索引的子数组
- (nullable NSArray *)getStrategyDataAboutKAtIndex:(NSUInteger)index {
    NSArray *kArray = [self getStrategyDataAboutK];
    if (!kArray || index >= kArray.count) return nil;

    id item = kArray[index];
    if ([item isKindOfClass:[NSArray class]]) {
        return (NSArray *)item;
    }

    return nil;
}

// 通用的嵌套数据访问方法，支持点分隔的键路径
- (nullable id)getStrategyNestedValueForKeyPath:(NSString *)keyPath {
    if (!keyPath || !self.strategyData) return nil;

    __block id value = nil;
    dispatch_sync(self.dataQueue, ^{
        // 分割键路径
        NSArray *keys = [keyPath componentsSeparatedByString:@"."];
        if (keys.count == 0) return;

        // 从 strategyData 开始遍历
        id currentValue = self.strategyData;

        for (NSString *key in keys) {
            if ([currentValue isKindOfClass:[NSDictionary class]]) {
                NSDictionary *dict = (NSDictionary *)currentValue;
                currentValue = dict[key];
            } else {
                // 如果当前值不是字典，无法继续访问
                currentValue = nil;
                break;
            }
        }

        value = currentValue;
    });

    return value;
}

#pragma mark - 策略数据便捷访问方法

- (nullable NSString *)getStrategyH5BaseUrl {
    // 获取第7个子数组中的H5基础URL
    NSArray *urlGroup = [self getStrategyDataAboutKAtIndex:7];
    if (urlGroup && urlGroup.count > 0) {
        id urlItem = urlGroup[0];
        if ([urlItem isKindOfClass:[NSString class]]) {
            return (NSString *)urlItem;
        }
    }
    return nil;
}

// 获取消息处理器名称数组（从 data.about.k[5] 获取）
- (nullable NSArray *)getStrategyMessageHandlers {
    // 获取第5个子数组中的消息处理器名称
    NSArray *messageHandlers = [self getStrategyDataAboutKAtIndex:5];
    if (messageHandlers && [messageHandlers isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in messageHandlers) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"消息处理器数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return messageHandlers;
    }
    return nil;
}

// 获取事件动作名称数组（从 data.about.k[0] 获取）
- (nullable NSArray *)getStrategyEventActions {
    // 获取第0个子数组中的事件动作名称
    NSArray *eventActions = [self getStrategyDataAboutKAtIndex:0];
    if (eventActions && [eventActions isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in eventActions) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"事件动作数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return eventActions;
    }
    return nil;
}

// 获取事件参数键名数组（从 data.about.k[2] 获取）
- (nullable NSArray *)getStrategyEventKeys {
    // 获取第2个子数组中的事件参数键名
    NSArray *eventKeys = [self getStrategyDataAboutKAtIndex:2];
    if (eventKeys && [eventKeys isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in eventKeys) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"事件参数键名数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return eventKeys;
    }
    return nil;
}

// 获取应用事件名称数组（从 data.about.k[3] 获取）
- (nullable NSArray *)getStrategyAppEvents {
    // 获取第3个子数组中的应用事件名称
    NSArray *appEvents = [self getStrategyDataAboutKAtIndex:3];
    if (appEvents && [appEvents isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in appEvents) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"应用事件数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return appEvents;
    }
    return nil;
}

// 获取配置属性名称数组（从 data.about.k[1] 获取）
- (nullable NSArray *)getStrategyConfigProperties {
    // 获取第1个子数组中的配置属性名称
    NSArray *configProperties = [self getStrategyDataAboutKAtIndex:1];
    if (configProperties && [configProperties isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in configProperties) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"配置属性数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return configProperties;
    }
    return nil;
}

- (nullable NSArray *)getStrategyAllKeys {
    // 获取第1个子数组中的配置属性名称
    NSArray *allKeys = [self getStrategyDataAboutKAtIndex:4];
    if (allKeys && [allKeys isKindOfClass:[NSArray class]]) {
        // 验证数组中的元素都是字符串
        for (id item in allKeys) {
            if (![item isKindOfClass:[NSString class]]) {
                NSLog(@"AllKeys数组包含非字符串元素: %@", item);
                return nil;
            }
        }
        return allKeys;
    }
    return nil;
}

#pragma mark - 归因数据管理

- (void)setAttributionData:(nullable NSDictionary *)attributionData {
    dispatch_async(self.dataQueue, ^{
        self->_attributionData = attributionData;

        // 通知数据变化
        [self notifyDataChange:@"attributionData" newData:attributionData];
    });
}

#pragma mark - 数据持久化

- (void)saveAllDataToCache {
    dispatch_async(self.dataQueue, ^{
        if (self.appConfigData) {
            [self.userDefaultsManager setAppConfigData:self.appConfigData];
        }
        if (self.authData) {
            [self.userDefaultsManager setAuthData:self.authData];
        }
        if (self.strategyData) {
            [self.userDefaultsManager setStrategyData:self.strategyData];
        }
    });
}

- (void)loadAllDataFromCache {
    dispatch_async(self.dataQueue, ^{
        // 加载AppConfig数据
        NSDictionary *cachedAppConfig = [self.userDefaultsManager getAppConfigData];
        if (cachedAppConfig) {
            self->_appConfigData = cachedAppConfig;
        }

        // 加载认证数据
        NSDictionary *cachedAuthData = [self.userDefaultsManager getAuthData];
        if (cachedAuthData) {
            self->_authData = cachedAuthData;
            self->_authToken = [self stringFromDictionary:cachedAuthData forKey:@"token"];
            self->_userInfo = [self dictionaryFromDictionary:cachedAuthData forKey:@"userInfo"];
        }

        // 加载策略数据
        NSDictionary *cachedStrategyData = [self.userDefaultsManager getStrategyData];
        if (cachedStrategyData) {
            self->_strategyData = cachedStrategyData;
        }
    });
}

- (void)clearAllData {
    dispatch_async(self.dataQueue, ^{
        self->_appConfigData = nil;
        self->_authData = nil;
        self->_authToken = nil;
        self->_userInfo = nil;
        self->_strategyData = nil;
        self->_attributionData = nil;

        // 清除缓存
        [self.userDefaultsManager clearAppConfigData];
        [self.userDefaultsManager clearAuthData];
        [self.userDefaultsManager clearStrategyData];

        // 通知数据变化
        [self notifyDataChange:@"allData" newData:nil];
    });
}



#pragma mark - 私有方法

- (void)notifyDataChange:(NSString *)dataType newData:(id)newData {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (self.dataChangeHandler) {
            self.dataChangeHandler(dataType, newData);
        }
    });
}

@end
