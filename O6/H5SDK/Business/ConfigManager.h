//
//  ConfigManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ConfigManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// AppConfig便捷访问方法
- (nullable NSString *)getEncryptKey;

// App扩展数据访问
- (nullable NSString *)getH5FullPath;

// 风控配置访问
- (nullable NSDictionary *)getRiskControlInfoConfig;
- (NSInteger)getKInterval;
- (nullable NSString *)getKFactorNum;
- (nullable NSString *)getKFactor;

// 配置项检查
- (BOOL)hasValidAppConfig;
- (BOOL)hasValidEncryptionConfig;
- (BOOL)hasValidH5Config;

// 配置更新通知
- (void)refreshConfigData;

@end

NS_ASSUME_NONNULL_END
