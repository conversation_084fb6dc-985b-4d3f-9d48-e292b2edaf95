//
//  ConfigManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "ConfigManager.h"
#import "GlobalDataManager.h"

@interface ConfigManager ()

@property (nonatomic, strong) GlobalDataManager *globalDataManager;

@end

@implementation ConfigManager

// 辅助方法：从字典中安全获取字符串
- (nullable NSString *)stringFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSString class]]) {
        return (NSString *)value;
    }
    if ([value isKindOfClass:[NSNumber class]]) {
        return [(NSNumber *)value stringValue];
    }
    return nil;
}

// 辅助方法：从字典中安全获取数字
- (nullable NSNumber *)numberFromDictionary:(NSDictionary *)dict forKey:(NSString *)key {
    if (!dict || !key) return nil;
    id value = dict[key];
    if ([value isKindOfClass:[NSNumber class]]) {
        return (NSNumber *)value;
    }
    if ([value isKindOfClass:[NSString class]]) {
        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
        return [formatter numberFromString:(NSString *)value];
    }
    return nil;
}

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static ConfigManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.globalDataManager = [GlobalDataManager sharedInstance];
    }
    return self;
}

#pragma mark - AppConfig便捷访问方法

- (nullable NSString *)getEncryptKey {
    // 优先从items数组获取，fallback到根级别
    NSString *value = [self.globalDataManager getAppConfigItemDataByName:@"encrypt_key"];   
    return value;
}

#pragma mark - App扩展数据访问

- (nullable NSString *)getH5FullPath {
    // 从策略数据中获取H5基础URL
    return [self.globalDataManager getStrategyH5BaseUrl];
}

#pragma mark - 风控配置访问

- (nullable NSDictionary *)getRiskControlInfoConfig {
    return [self.globalDataManager getAppConfigValueForKey:@"riskControlInfoConfig"];
}

- (NSInteger)getKInterval {
    NSDictionary *riskConfig = [self getRiskControlInfoConfig];
    if (riskConfig) {
        NSNumber *interval = [self numberFromDictionary:riskConfig forKey:@"k_interval"];
        return interval ? [interval integerValue] : 5; // 默认值5
    }
    return 5;
}

- (nullable NSString *)getKFactorNum {
    NSDictionary *riskConfig = [self getRiskControlInfoConfig];
    if (riskConfig) {
        return [self stringFromDictionary:riskConfig forKey:@"k_factor_num"];
    }
    return nil;
}

- (nullable NSString *)getKFactor {
    NSDictionary *riskConfig = [self getRiskControlInfoConfig];
    if (riskConfig) {
        return [self stringFromDictionary:riskConfig forKey:@"k_factor"];
    }
    return nil;
}

#pragma mark - 配置项检查

- (BOOL)hasValidAppConfig {
    return self.globalDataManager.appConfigData != nil;
}

- (BOOL)hasValidEncryptionConfig {
    NSString *encryptKey = [self getEncryptKey];
    return encryptKey != nil && encryptKey.length > 0;
}

- (BOOL)hasValidH5Config {
    NSString *h5FullPath = [self getH5FullPath];
    return h5FullPath != nil && h5FullPath.length > 0;
}

#pragma mark - 配置更新通知

- (void)refreshConfigData {
    // 触发GlobalDataManager重新加载配置数据
    [self.globalDataManager loadAllDataFromCache];
}

@end
