//
//  PaymentManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN

// 支付状态枚举
typedef NS_ENUM(NSInteger, PaymentState) {
    PaymentStateIdle,                   // 空闲状态
    PaymentStateQueryingProducts,       // 查询商品
    PaymentStateCreatingOrder,          // 创建订单
    PaymentStatePurchasing,             // 购买中
    PaymentStateVerifying,              // 验证购买
    PaymentStateCompleted,              // 支付完成
    PaymentStateFailed,                 // 支付失败
    PaymentStateCancelled               // 支付取消
};

// 支付结果回调
typedef void(^PaymentStateChangeHandler)(PaymentState state, NSDictionary * _Nullable userInfo);
typedef void(^PaymentCompletionHandler)(BOOL success, NSError * _Nullable error);

@interface PaymentManager : NSObject <SKProductsRequestDelegate, SKPaymentTransactionObserver>

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 当前状态
@property (nonatomic, assign, readonly) PaymentState currentState;
@property (nonatomic, assign, readonly) BOOL isPaymentInProgress;

// 状态变化回调
@property (nonatomic, copy, nullable) PaymentStateChangeHandler stateChangeHandler;

// 支付方法
- (void)purchaseProduct:(NSString *)productCode 
                 source:(nullable NSString *)source 
                  entry:(nullable NSString *)entry 
             completion:(PaymentCompletionHandler)completion;

// 恢复购买
- (void)restorePurchasesWithCompletion:(PaymentCompletionHandler)completion;

// 查询商品信息
- (void)queryProductInfo:(NSString *)productCode 
              completion:(void(^)(SKProduct * _Nullable product, NSError * _Nullable error))completion;

// 状态查询
- (NSString *)stateDescription:(PaymentState)state;
- (BOOL)canTransitionToState:(PaymentState)newState;

// 取消支付
- (void)cancelCurrentPayment;

// 重置支付状态
- (void)resetPaymentState;

@end

NS_ASSUME_NONNULL_END
