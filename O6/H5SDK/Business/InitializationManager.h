//
//  InitializationManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 初始化状态枚举
typedef NS_ENUM(NSInteger, InitializationState) {
    InitializationStateIdle,                    // 空闲状态
    InitializationStateInitializingSDK,         // 初始化第三方SDK
    InitializationStateWaitingNetwork,          // 等待网络连接
    InitializationStateLoadingConfig,           // 加载应用配置
    InitializationStateAuthenticating,          // 用户认证
    InitializationStateLoadingStrategy,         // 加载策略数据
    InitializationStateValidatingStrategy,      // 验证策略数据
    InitializationStateCompleted,               // 初始化完成
    InitializationStateFailed                   // 初始化失败
};

// 状态变化回调
typedef void(^StateChangeHandler)(InitializationState state, NSDictionary * _Nullable userInfo);
typedef void(^InitializationCompletionHandler)(BOOL success, NSError * _Nullable error);

@interface InitializationManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 当前状态
@property (nonatomic, assign, readonly) InitializationState currentState;
@property (nonatomic, assign, readonly) BOOL isInitializing;
@property (nonatomic, assign, readonly) BOOL isInitialized;
@property (nonatomic, assign, readonly) BOOL isThirdPartySDKInitialized;

// 状态变化回调
@property (nonatomic, copy, nullable) StateChangeHandler stateChangeHandler;

// 初始化方法
- (void)startInitializationWithCompletion:(InitializationCompletionHandler)completion;
- (void)retryInitialization;
- (void)cancelInitialization;

// 状态查询
- (NSString *)stateDescription:(InitializationState)state;
- (BOOL)canTransitionToState:(InitializationState)newState;

// 重置初始化状态
- (void)resetInitializationState;

@end

NS_ASSUME_NONNULL_END
