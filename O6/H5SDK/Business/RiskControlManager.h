//
//  RiskControlManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface RiskControlManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 生成风控数据
- (NSDictionary *)generateRiskControlData;
- (NSDictionary *)generateRiskControlDataWithUserId:(nullable NSString *)userId;

// 加密风控数据
- (nullable NSString *)encryptRiskControlData:(NSDictionary *)riskData withKey:(NSString *)key;

// 上报风控数据
- (void)reportRiskControlDataWithKey:(NSString *)encryptionKey 
                          completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

- (void)reportRiskControlDataWithUserId:(nullable NSString *)userId 
                                    key:(NSString *)encryptionKey 
                             completion:(void(^)(BOOL success, NSError * _Nullable error))completion;

@end

NS_ASSUME_NONNULL_END
