//
//  ThirdPartySDKManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "ThirdPartySDKManager.h"
#import "Constants.h"
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <Adjust/Adjust.h>
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import "APIClient.h"
#import "DeviceInfoManager.h"
#import "GlobalDataManager.h"

@interface ThirdPartySDKManager () <AdjustDelegate>

@property (nonatomic, assign) BOOL adjustInitialized;
@property (nonatomic, assign) BOOL facebookInitialized;
@property (nonatomic, strong) NSDictionary *cachedAttribution;
@property (nonatomic, strong) dispatch_queue_t sdkQueue;

@end

@implementation ThirdPartySDKManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static ThirdPartySDKManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.adjustInitialized = NO;
        self.facebookInitialized = NO;
        self.sdkQueue = dispatch_queue_create([Constants thirdPartyQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

#pragma mark - SDK初始化

- (void)initializeSDKsWithCompletion:(InitializationCompletionHandler)completion {
    // 先请求跟踪权限
    [self requestTrackingPermissionWithCompletion:^(BOOL granted) {
        dispatch_async(self.sdkQueue, ^{
            dispatch_group_t group = dispatch_group_create();
            __block NSError *initError = nil;
            
            // 初始化Adjust
            dispatch_group_enter(group);
            [self initializeAdjustWithCompletion:^(BOOL success, NSError *error) {
                if (!success && error) {
                    initError = error;
                }
                dispatch_group_leave(group);
            }];
            
            // 初始化Facebook
            dispatch_group_enter(group);
            [self initializeFacebookWithCompletion:^(BOOL success, NSError *error) {
                if (!success && error && !initError) {
                    initError = error;
                }
                dispatch_group_leave(group);
            }];
            
            // 等待所有初始化完成
            dispatch_group_notify(group, dispatch_get_main_queue(), ^{
                BOOL success = self.adjustInitialized && self.facebookInitialized;
                if (completion) {
                    completion(success, initError);
                }
            });
        });
    }];
}

- (void)requestTrackingPermissionWithCompletion:(void(^)(BOOL granted))completion {
    if (@available(iOS 14, *)) {
        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            BOOL granted = (status == ATTrackingManagerAuthorizationStatusAuthorized);
            dispatch_async(dispatch_get_main_queue(), ^{
                if (completion) {
                    completion(granted);
                }
            });
        }];
    } else {
        // iOS 14以下版本直接返回YES
        if (completion) {
            completion(YES);
        }
    }
}

#pragma mark - Adjust相关

- (void)initializeAdjustWithCompletion:(InitializationCompletionHandler)completion {
    dispatch_async(self.sdkQueue, ^{
        @try {
            // 真实的Adjust初始化代码
            NSString *appToken = [Constants adjustAppToken];
            ADJConfig *adjustConfig = [ADJConfig configWithAppToken:appToken environment:ADJEnvironmentSandbox];
            adjustConfig.logLevel = ADJLogLevelVerbose;

            // 设置归因回调代理
            adjustConfig.delegate = self;

            [Adjust appDidLaunch:adjustConfig];

            // 初始化成功
            self.adjustInitialized = YES;
            NSLog(@"Adjust SDK初始化成功");

            dispatch_async(dispatch_get_main_queue(), ^{
                if (completion) {
                    completion(YES, nil);
                }
            });
            
        } @catch (NSException *exception) {
            self.adjustInitialized = NO;
            NSLog(@"Adjust SDK初始化失败: %@", exception.reason);
            NSError *error = [NSError errorWithDomain:@"AdjustInitError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: exception.reason ?: @"Adjust初始化失败"}];
            dispatch_async(dispatch_get_main_queue(), ^{
                if (completion) {
                    completion(NO, error);
                }
            });
        }
    });
}

- (nullable NSDictionary *)getAdjustAttribution {
    __block NSDictionary *result = nil;

    dispatch_sync(self.sdkQueue, ^{
        if (!self.adjustInitialized) {
            result = nil;
            return;
        }

        // 真实的Adjust归因数据获取
        ADJAttribution *attribution = [Adjust attribution];
        NSDictionary *attributionDict = [self convertAdjustAttribution:attribution];

        // 如果没有归因数据，使用缓存的数据
        if (!attributionDict && self.cachedAttribution) {
            attributionDict = self.cachedAttribution;
        }

        result = attributionDict;
    });

    return result;
}

- (void)getAdjustAttributionWithCompletion:(AttributionCompletionHandler)completion {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSDictionary *attribution = [self getAdjustAttribution];
        if (completion) {
            completion(attribution);
        }
    });
}

#pragma mark - 事件上报

- (void)trackPurchaseEvent:(NSString *)productCode
                    amount:(NSNumber *)amount
                  currency:(NSString *)currency {
    if (!productCode || !amount || !currency) return;

    dispatch_async(self.sdkQueue, ^{
        // Adjust购买事件
        if (self.adjustInitialized) {
            NSString *purchaseToken = [Constants adjustPurchaseEvent];
            if (purchaseToken && purchaseToken.length > 0) {
                ADJEvent *adjustEvent = [ADJEvent eventWithEventToken:purchaseToken];
                [adjustEvent setRevenue:[amount doubleValue] currency:currency];
                [adjustEvent addCallbackParameter:@"product_code" value:productCode];
                [Adjust trackEvent:adjustEvent];
                NSLog(@"Adjust购买事件上报成功: %@ - %@%@", productCode, amount, currency);
            } else {
                NSLog(@"Adjust购买事件Token为空，跳过上报");
            }
        }

        // Facebook购买事件
        if (self.facebookInitialized) {
            [FBSDKAppEvents.shared logPurchase:[amount doubleValue]
                                      currency:currency
                                    parameters:@{@"product_code": productCode}];
            NSLog(@"Facebook购买事件上报成功: %@ - %@%@", productCode, amount, currency);
        }
    });
}

- (void)trackOrderEvent:(NSString *)orderNo
                 amount:(NSNumber *)amount
               currency:(NSString *)currency {
    if (!orderNo || !amount || !currency) return;

    dispatch_async(self.sdkQueue, ^{
        // Adjust下单事件
        if (self.adjustInitialized) {
            NSString *orderToken = [Constants adjustOrderEvent];
            if (orderToken && orderToken.length > 0) {
                ADJEvent *adjustEvent = [ADJEvent eventWithEventToken:orderToken];
                [adjustEvent setRevenue:[amount doubleValue] currency:currency];
                [adjustEvent addCallbackParameter:@"order_no" value:orderNo];
                [Adjust trackEvent:adjustEvent];
                NSLog(@"Adjust下单事件上报成功: %@ - %@%@", orderNo, amount, currency);
            } else {
                NSLog(@"Adjust下单事件Token为空，跳过上报");
            }
        }

        // Facebook下单事件
        if (self.facebookInitialized) {
            [FBSDKAppEvents.shared logEvent:@"InitiatedCheckout"
                                 valueToSum:[amount doubleValue]
                                 parameters:@{
                                     @"order_no": orderNo,
                                     @"currency": currency
                                 }];
            NSLog(@"Facebook下单事件上报成功: %@ - %@%@", orderNo, amount, currency);
        }
    });
}

- (void)trackLoginEvent {
    dispatch_async(self.sdkQueue, ^{
        // 只上报Adjust登录事件
        if (self.adjustInitialized) {
            NSString *loginToken = [Constants adjustLoginEvent];
            if (loginToken && loginToken.length > 0) {
                ADJEvent *adjustEvent = [ADJEvent eventWithEventToken:loginToken];
                [Adjust trackEvent:adjustEvent];
                NSLog(@"Adjust登录事件上报成功");
            } else {
                NSLog(@"Adjust登录事件Token为空，跳过上报");
            }
        }
    });
}

- (void)trackRegisterEvent {
    dispatch_async(self.sdkQueue, ^{
        // 只上报Adjust注册事件
        if (self.adjustInitialized) {
            NSString *registerToken = [Constants adjustRegisterEvent];
            if (registerToken && registerToken.length > 0) {
                ADJEvent *adjustEvent = [ADJEvent eventWithEventToken:registerToken];
                [Adjust trackEvent:adjustEvent];
                NSLog(@"Adjust注册事件上报成功");
            } else {
                NSLog(@"Adjust注册事件Token为空，跳过上报");
            }
        }
    });
}

#pragma mark - 归因数据上报

- (void)reportAttributionDataWithCompletion:(void(^)(BOOL success))completion {
    // 使用同步方法获取归因数据
    NSDictionary *attribution = [self getAdjustAttribution];

    if (attribution) {
        // 构建完整的归因上报参数
        NSMutableDictionary *params = [NSMutableDictionary dictionary];

        // 获取管理器实例
        DeviceInfoManager *deviceInfo = [DeviceInfoManager sharedInstance];
        GlobalDataManager *globalData = [GlobalDataManager sharedInstance];

        // 归因数据映射
        params[@"utmSource"] = attribution[@"network"] ?: @"";
        params[@"campaignId"] = attribution[@"campaign"] ?: @"";
        params[@"adsetId"] = attribution[@"creative"] ?: @"";
        params[@"adgroupId"] = attribution[@"adgroup"] ?: @"";

        params[@"ver"] = deviceInfo.appVersion;

        // 归因SDK信息
        params[@"attributionSdk"] = @"AJ";
        params[@"attributionSdkVer"] = [Adjust sdkVersion] ?: @"";

        // 添加用户信息（如果已登录）
        params[@"userId"] = globalData.userId ?: @"";

        params[@"pkg"] = deviceInfo.bundleIdentifier;

        // 设备信息
        params[@"device-id"] = deviceInfo.deviceId;

        NSLog(@"归因数据上报参数: %@", params);

        [[APIClient sharedInstance] reportAttributionWithData:[params copy] completion:^(BOOL success, id  _Nullable data, NSError * _Nullable error) {
            if (completion) {
                completion(success);
            }
        }];
    } else {
        if (completion) {
            completion(NO);
        }
    }
}

#pragma mark - 私有方法

- (NSDictionary *)convertAdjustAttribution:(ADJAttribution *)attribution {
    if (!attribution) return nil;

    return @{
        @"network": attribution.network ?: @"",
        @"campaign": attribution.campaign ?: @"",
        @"adgroup": attribution.adgroup ?: @"",
        @"creative": attribution.creative ?: @"",
    };
}

#pragma mark - SDK状态检查

- (BOOL)isAdjustInitialized {
    return self.adjustInitialized;
}

- (BOOL)isFacebookInitialized {
    return self.facebookInitialized;
}

- (BOOL)isAllSDKsInitialized {
    return self.adjustInitialized && self.facebookInitialized;
}

#pragma mark - 自定义事件跟踪

- (void)trackCustomEvent:(NSString *)eventName parameters:(nullable NSDictionary *)parameters {
    if (!eventName || eventName.length == 0) return;

    dispatch_async(self.sdkQueue, ^{
        if (self.facebookInitialized) {
            [FBSDKAppEvents.shared logEvent:eventName parameters:parameters];
            NSLog(@"自定义事件上报: %@", eventName);
        }
    });
}

- (void)trackCustomEventWithRevenue:(NSString *)eventName
                             amount:(NSNumber *)amount
                           currency:(NSString *)currency
                         parameters:(nullable NSDictionary *)parameters {
    if (!eventName || eventName.length == 0 || !amount || !currency) return;

    dispatch_async(self.sdkQueue, ^{
        if (self.facebookInitialized) {
            NSMutableDictionary *eventParams = parameters ? [parameters mutableCopy] : [NSMutableDictionary dictionary];
            eventParams[@"currency"] = currency;

            [FBSDKAppEvents.shared logEvent:eventName
                                 valueToSum:[amount doubleValue]
                                 parameters:eventParams];
            NSLog(@"Facebook自定义收入事件上报: %@", eventName);
        }
    });
}

#pragma mark - Facebook相关

- (void)initializeFacebookWithCompletion:(InitializationCompletionHandler)completion {
    // Facebook SDK初始化需要在主线程执行，因为会调用UIApplication相关API
    dispatch_async(dispatch_get_main_queue(), ^{
        @try {
            NSString *facebookAppId = [Constants facebookAppId];
            if (!facebookAppId || facebookAppId.length == 0) {
                NSLog(@"Facebook App ID未配置，跳过Facebook SDK初始化");
                self.facebookInitialized = NO;
                if (completion) {
                    NSError *error = [NSError errorWithDomain:@"FacebookInitError"
                                                         code:-1003
                                                     userInfo:@{NSLocalizedDescriptionKey: @"Facebook App ID未配置"}];
                    completion(NO, error);
                }
                return;
            }

            [FBSDKSettings.sharedSettings setAppID:facebookAppId];
            [FBSDKApplicationDelegate.sharedInstance application:[UIApplication sharedApplication]
                                   didFinishLaunchingWithOptions:nil];

            self.facebookInitialized = YES;
            NSLog(@"Facebook SDK初始化成功");

            if (completion) {
                completion(YES, nil);
            }

        } @catch (NSException *exception) {
            self.facebookInitialized = NO;
            NSLog(@"Facebook SDK初始化失败: %@", exception.reason);
            NSError *error = [NSError errorWithDomain:@"FacebookInitError"
                                                 code:-1002
                                             userInfo:@{NSLocalizedDescriptionKey: exception.reason ?: @"Facebook初始化失败"}];
            if (completion) {
                completion(NO, error);
            }
        }
    });
}

#pragma mark - AdjustDelegate

- (void)adjustAttributionChanged:(ADJAttribution *)attribution {
    self.cachedAttribution = [self convertAdjustAttribution:attribution];
    [self reportAttributionDataWithCompletion:^(BOOL success) {
            
    }];
    NSLog(@"Adjust归因数据更新: %@", self.cachedAttribution);
}

@end
