//
//  RiskControlManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "RiskControlManager.h"
#import "DeviceInfoManager.h"
#import "CryptoUtils.h"
#import "APIClient.h"

@interface RiskControlManager ()

@property (nonatomic, strong) DeviceInfoManager *deviceInfoManager;
@property (nonatomic, strong) CryptoUtils *cryptoUtils;

@end

@implementation RiskControlManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static RiskControlManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.deviceInfoManager = [DeviceInfoManager sharedInstance];
        self.cryptoUtils = [CryptoUtils sharedInstance];
    }
    return self;
}

#pragma mark - 生成风控数据

- (NSDictionary *)generateRiskControlData {
    return [self generateRiskControlDataWithUserId:nil];
}

- (NSDictionary *)generateRiskControlDataWithUserId:(nullable NSString *)userId {
    NSMutableDictionary *riskData = [NSMutableDictionary dictionary];
    
    // 平台信息
    riskData[@"platform"] = @"iOS";
    riskData[@"pkg"] = self.deviceInfoManager.bundleIdentifier;
    riskData[@"ver"] = self.deviceInfoManager.appVersion;
    riskData[@"platform_ver"] = self.deviceInfoManager.systemVersion;
    riskData[@"model"] = self.deviceInfoManager.deviceModel;
    
    // 用户和设备信息
    riskData[@"user_id"] = userId ?: @"";
    riskData[@"device_id"] = self.deviceInfoManager.deviceId;
    riskData[@"system_language"] = self.deviceInfoManager.languageCode;
    riskData[@"time_zone"] = self.deviceInfoManager.timeZone;
    
    // 安全检测信息
    riskData[@"is_enable_vpn"] = @(self.deviceInfoManager.isVPNEnabled ? 1 : 0);
    riskData[@"is_enable_proxy"] = @(self.deviceInfoManager.isProxyEnabled ? 1 : 0);
    riskData[@"is_jailbreaking"] = @(self.deviceInfoManager.isJailbroken ? 1 : 0);
    riskData[@"is_emulator"] = @(self.deviceInfoManager.isSimulator ? 1 : 0);
    
    return [riskData copy];
}

#pragma mark - 加密风控数据

- (nullable NSString *)encryptRiskControlData:(NSDictionary *)riskData withKey:(NSString *)key {
    if (!riskData || !key) {
        return nil;
    }
    
    return [self.cryptoUtils encryptRiskControlData:riskData withKey:key];
}

#pragma mark - 上报风控数据

- (void)reportRiskControlDataWithKey:(NSString *)encryptionKey 
                          completion:(void(^)(BOOL success, NSError * _Nullable error))completion {
    [self reportRiskControlDataWithUserId:nil key:encryptionKey completion:completion];
}

- (void)reportRiskControlDataWithUserId:(nullable NSString *)userId 
                                    key:(NSString *)encryptionKey 
                             completion:(void(^)(BOOL success, NSError * _Nullable error))completion {
    
    if (!encryptionKey) {
        NSError *error = [NSError errorWithDomain:@"RiskControlError"
                                             code:-1001
                                         userInfo:@{NSLocalizedDescriptionKey: @"Encryption key cannot be empty"}];
        if (completion) {
            completion(NO, error);
        }
        return;
    }
    
    // 生成风控数据
    NSDictionary *riskData = [self generateRiskControlDataWithUserId:userId];
    
    // 加密风控数据
    NSString *encryptedData = [self encryptRiskControlData:riskData withKey:encryptionKey];
    if (!encryptedData) {
        NSError *error = [NSError errorWithDomain:@"RiskControlError"
                                             code:-1002
                                         userInfo:@{NSLocalizedDescriptionKey: @"Risk control data encryption failed"}];
        if (completion) {
            completion(NO, error);
        }
        return;
    }
    
    // 调用APIClient进行风控数据上报
    [[APIClient sharedInstance] reportRiskControlWithData:encryptedData completion:^(BOOL success, id _Nullable data, NSError * _Nullable error) {
        if (completion) {
            completion(success, error);
        }
    }];
}

@end
