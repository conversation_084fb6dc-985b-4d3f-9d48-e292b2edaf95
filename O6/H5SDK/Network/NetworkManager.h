//
//  NetworkManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 网络请求完成回调
typedef void(^NetworkCompletionBlock)(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error);

// JSON请求完成回调
typedef void(^JSONCompletionBlock)(id _Nullable jsonObject, NSError * _Nullable error);

@interface NetworkManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 基础网络请求
- (NSURLSessionDataTask *)performRequest:(NSURLRequest *)request
                              completion:(NetworkCompletionBlock)completion;



// POST请求
- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(nullable NSDictionary *)parameters
                       headers:(nullable NSDictionary *)headers
                    completion:(NetworkCompletionBlock)completion;



// JSON POST请求
- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(nullable NSDictionary *)parameters
                       headers:(nullable NSDictionary *)headers
                JSONCompletion:(JSONCompletionBlock)completion;

// 网络状态检查
- (BOOL)isNetworkReachable;

// 取消所有请求
- (void)cancelAllTasks;

@end

NS_ASSUME_NONNULL_END
