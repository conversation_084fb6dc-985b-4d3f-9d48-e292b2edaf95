//
//  CacheService.h
//  H5SDK
//
//  Created by H5SD<PERSON> on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 缓存策略协议
@protocol CacheStrategy <NSObject>

- (void)setObject:(id)object forKey:(NSString *)key;
- (nullable id)objectForKey:(NSString *)key;
- (void)removeObjectForKey:(NSString *)key;
- (void)removeAllObjects;
- (BOOL)containsObjectForKey:(NSString *)key;

@end

// 内存缓存策略
@interface MemoryCacheStrategy : NSObject <CacheStrategy>

- (instancetype)initWithCapacity:(NSUInteger)capacity;

@end

// 磁盘缓存策略
@interface DiskCacheStrategy : NSObject <CacheStrategy>

- (instancetype)initWithCacheDirectory:(NSString *)directory;

@end

// 混合缓存策略（内存+磁盘）
@interface HybridCacheStrategy : NSObject <CacheStrategy>

- (instancetype)initWithMemoryCapacity:(NSUInteger)memoryCapacity 
                        cacheDirectory:(NSString *)directory;

@end

// 缓存服务主类
@interface CacheService : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 策略设置
@property (nonatomic, strong) id<CacheStrategy> cacheStrategy;

// 缓存操作
- (void)setObject:(id)object forKey:(NSString *)key;
- (void)setObject:(id)object forKey:(NSString *)key withExpiration:(NSTimeInterval)expiration;
- (nullable id)objectForKey:(NSString *)key;
- (void)removeObjectForKey:(NSString *)key;
- (void)removeAllObjects;
- (BOOL)containsObjectForKey:(NSString *)key;

// 过期检查
- (BOOL)isExpiredForKey:(NSString *)key;
- (void)removeExpiredObjects;

// 缓存统计
- (NSUInteger)cacheSize;
- (void)clearExpiredCache;

@end

NS_ASSUME_NONNULL_END
