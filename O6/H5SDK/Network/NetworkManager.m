//
//  NetworkManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/29.
//

#import "NetworkManager.h"
#import <SystemConfiguration/SystemConfiguration.h>
#import <netinet/in.h>
#import "CryptoUtils.h"
#import "GlobalDataManager.h"
#import "ConfigManager.h"
#import "Constants.h"

@interface NetworkManager ()

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, strong) NSMutableSet<NSURLSessionDataTask *> *activeTasks;

@end

@implementation NetworkManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static NetworkManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        [self setupSession];
        self.activeTasks = [NSMutableSet set];
    }
    return self;
}

- (void)setupSession {
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = 30.0;
    config.timeoutIntervalForResource = 60.0;
    config.requestCachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
    
    self.session = [NSURLSession sessionWithConfiguration:config];
}

#pragma mark - 基础网络请求

- (NSURLSessionDataTask *)performRequest:(NSURLRequest *)request
                              completion:(NetworkCompletionBlock)completion {
    if (!request) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"NetworkManagerError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"请求对象不能为空"}];
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(nil, nil, error);
            });
        }
        return nil;
    }
    
    __block NSURLSessionDataTask *task = nil;
    task = [self.session dataTaskWithRequest:request
                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        // 从活跃任务集合中移除
        @synchronized (self.activeTasks) {
            [self.activeTasks removeObject:task];
        }

        // 回调到主线程
        dispatch_async(dispatch_get_main_queue(), ^{
            if (completion) {
                completion(data, response, error);
            }
        });
    }];
    
    // 添加到活跃任务集合
    @synchronized (self.activeTasks) {
        [self.activeTasks addObject:task];
    }
    
    [task resume];
    return task;
}



#pragma mark - POST请求

- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(nullable NSDictionary *)parameters
                       headers:(nullable NSDictionary *)headers
                    completion:(NetworkCompletionBlock)completion {
    
    NSURLRequest *request = [self buildPOSTRequest:URLString parameters:parameters headers:headers];
    if (!request) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"NetworkManagerError"
                                                 code:-1003
                                             userInfo:@{NSLocalizedDescriptionKey: @"构建POST请求失败"}];
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(nil, nil, error);
            });
        }
        return nil;
    }
    
    return [self performRequest:request completion:completion];
}

#pragma mark - JSON请求



- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(nullable NSDictionary *)parameters
                       headers:(nullable NSDictionary *)headers
                JSONCompletion:(JSONCompletionBlock)completion {

    return [self POST:URLString parameters:parameters headers:headers completion:^(NSData *data, NSURLResponse *response, NSError *error) {
        [self handleJSONResponse:data error:error completion:completion forURL:URLString];
    }];
}

#pragma mark - 加密相关私有方法

- (nullable NSString *)getEncryptionKeyForURL:(NSString *)URLString {
    if ([URLString containsString:[Constants appConfigPath]]) {
        // getAppConfig接口使用baseURL的host作为key
        NSURL *baseURL = [NSURL URLWithString:[Constants baseURL]];
        return baseURL.host;
    } else {
        // 其他接口从ConfigManager获取encrypt_key
        return [[ConfigManager sharedInstance] getEncryptKey];
    }
}

- (nullable NSData *)encryptParameters:(NSDictionary *)parameters
                               headers:(NSDictionary *)headers
                                forURL:(NSString *)URLString {
    // 获取加密key
    NSString *encryptionKey = [self getEncryptionKeyForURL:URLString];
    if (!encryptionKey) {
        return nil;
    }

    // 合并参数
    NSMutableDictionary *mergedParams = [NSMutableDictionary dictionary];
    if (parameters) {
        [mergedParams addEntriesFromDictionary:parameters];
    }
    if (headers) {
        mergedParams[@"http_headers"] = headers;
    }

    // 转换为JSON字符串
    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:mergedParams options:0 error:&jsonError];
    if (jsonError || !jsonData) {
        return nil;
    }

    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    if (!jsonString) {
        return nil;
    }

    // AES加密
    CryptoUtils *cryptoUtils = [CryptoUtils sharedInstance];
    NSString *encryptedString = [cryptoUtils aesEncrypt:jsonString withKey:encryptionKey];
    if (!encryptedString) {
        return nil;
    }

    return [encryptedString dataUsingEncoding:NSUTF8StringEncoding];
}

- (nullable NSData *)decryptResponseData:(NSData *)responseData forURL:(NSString *)URLString {
    if (!responseData) {
        return nil;
    }

    // 获取加密key
    NSString *encryptionKey = [self getEncryptionKeyForURL:URLString];
    if (!encryptionKey) {
        return responseData; // 如果无法获取key，返回原始数据
    }

    // 转换为字符串并清理
    NSString *encryptedString = [[NSString alloc] initWithData:responseData encoding:NSUTF8StringEncoding];
    if (!encryptedString) {
        return responseData;
    }

    // 清理字符串
    NSString *cleanedString = [[encryptedString stringByReplacingOccurrencesOfString:@"\r\n" withString:@""]
                              stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];

    // AES解密
    CryptoUtils *cryptoUtils = [CryptoUtils sharedInstance];
    NSString *decryptedString = [cryptoUtils aesDecrypt:cleanedString withKey:encryptionKey];
    if (!decryptedString) {
        return responseData; // 解密失败返回原始数据
    }

    return [decryptedString dataUsingEncoding:NSUTF8StringEncoding];
}

- (id)decryptAppConfigData:(id)jsonObject {
    // 检查是否为有效的响应格式
    if (![jsonObject isKindOfClass:[NSDictionary class]]) {
        return jsonObject;
    }

    NSDictionary *response = (NSDictionary *)jsonObject;
    NSDictionary *data = response[@"data"];

    if (![data isKindOfClass:[NSDictionary class]]) {
        return jsonObject;
    }

    // 获取加密数据的键名
    NSString *key2 = [Constants key2]; // "Relaxed"
    NSString *key3 = [Constants key3]; // "Quantitative"
    NSString *key4 = [Constants key4]; // "Printed"

    NSString *value2 = data[key2];
    NSString *value3 = data[key3];
    NSString *value4 = data[key4];

    // 检查必要的字段是否存在
    if (![value2 isKindOfClass:[NSString class]] ||
        ![value3 isKindOfClass:[NSString class]] ||
        ![value4 isKindOfClass:[NSString class]]) {
        return jsonObject;
    }

    // Base64解码第2位和第3位的值
    NSData *data2 = [[NSData alloc] initWithBase64EncodedString:value2 options:0];
    NSData *data3 = [[NSData alloc] initWithBase64EncodedString:value3 options:0];

    if (!data2 || !data3) {
        return jsonObject;
    }

    // 合并成解密key
    NSMutableData *keyData = [NSMutableData dataWithData:data2];
    [keyData appendData:data3];
    NSString *decryptionKey = [[NSString alloc] initWithData:keyData encoding:NSUTF8StringEncoding];

    if (!decryptionKey) {
        return jsonObject;
    }

    // Base64解码第4位的值
    NSData *encryptedData = [[NSData alloc] initWithBase64EncodedString:value4 options:0];
    if (!encryptedData) {
        return jsonObject;
    }

    NSString *encryptedString = [[NSString alloc] initWithData:encryptedData encoding:NSUTF8StringEncoding];
    if (!encryptedString) {
        return jsonObject;
    }
    // 清理字符串
    NSString *cleanedString = [[encryptedString stringByReplacingOccurrencesOfString:@"\r\n" withString:@""]
                              stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    // 使用CryptoUtils进行AES解密
    CryptoUtils *cryptoUtils = [CryptoUtils sharedInstance];
    NSString *decryptedString = [cryptoUtils aesDecrypt:cleanedString withKey:decryptionKey];

    if (!decryptedString) {
        return jsonObject;
    }

    // 将解密结果转为字典
    NSData *decryptedData = [decryptedString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *parseError;
    id decryptedObject = [NSJSONSerialization JSONObjectWithData:decryptedData options:0 error:&parseError];

    if (parseError || ![decryptedObject isKindOfClass:[NSDictionary class]]) {
        return jsonObject;
    }

    // 创建新的响应，将解密结果重新赋值给data字段
    NSMutableDictionary *newResponse = [response mutableCopy];
    newResponse[@"data"] = decryptedObject;

    return [newResponse copy];
}

#pragma mark - 辅助方法



- (NSURLRequest *)buildPOSTRequest:(NSString *)URLString
                        parameters:(nullable NSDictionary *)parameters
                           headers:(nullable NSDictionary *)headers {

    if (!URLString || URLString.length == 0) {
        return nil;
    }

    NSURL *URL = [NSURL URLWithString:URLString];
    if (!URL) {
        return nil;
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:URL];
    request.HTTPMethod = @"POST";


    // 加密参数
    NSData *encryptedData = [self encryptParameters:parameters headers:headers forURL:URLString];
    if (encryptedData) {
        request.HTTPBody = encryptedData;
        [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    } else {
        // 加密失败的处理
        NSLog(@"参数加密失败，URL: %@", URLString);
        return nil;
    }

    return [request copy];
}





- (void)handleJSONResponse:(NSData *)data
                     error:(NSError *)networkError
                completion:(JSONCompletionBlock)completion
                    forURL:(NSString *)URLString {
    if (networkError) {
        if (completion) {
            completion(nil, networkError);
        }
        return;
    }

    if (!data) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"NetworkManagerError"
                                                 code:-1004
                                             userInfo:@{NSLocalizedDescriptionKey: @"响应数据为空"}];
            completion(nil, error);
        }
        return;
    }

    // 解密响应数据
    NSData *decryptedData = [self decryptResponseData:data forURL:URLString];

    NSError *parseError;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:decryptedData options:0 error:&parseError];

    // 如果是AppConfig接口，进行特殊解密处理
    if ([URLString containsString:[Constants appConfigPath]]) {
        jsonObject = [self decryptAppConfigData:jsonObject];
    }

    if (completion) {
        completion(jsonObject, parseError);
    }
}

#pragma mark - 网络状态检查

- (BOOL)isNetworkReachable {
    struct sockaddr_in zeroAddress;
    bzero(&zeroAddress, sizeof(zeroAddress));
    zeroAddress.sin_len = sizeof(zeroAddress);
    zeroAddress.sin_family = AF_INET;
    
    SCNetworkReachabilityRef reachability = SCNetworkReachabilityCreateWithAddress(kCFAllocatorDefault, (const struct sockaddr *)&zeroAddress);
    if (reachability == NULL) {
        return NO;
    }
    
    SCNetworkReachabilityFlags flags;
    BOOL success = SCNetworkReachabilityGetFlags(reachability, &flags);
    CFRelease(reachability);
    
    if (!success) {
        return NO;
    }
    
    BOOL isReachable = (flags & kSCNetworkReachabilityFlagsReachable) != 0;
    BOOL needsConnection = (flags & kSCNetworkReachabilityFlagsConnectionRequired) != 0;
    
    return isReachable && !needsConnection;
}

#pragma mark - 任务管理

- (void)cancelAllTasks {
    @synchronized (self.activeTasks) {
        for (NSURLSessionDataTask *task in self.activeTasks) {
            [task cancel];
        }
        [self.activeTasks removeAllObjects];
    }
}

@end
