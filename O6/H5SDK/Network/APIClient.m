//
//  APIClient.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/29.
//

#import "APIClient.h"
#import "NetworkManager.h"
#import "DeviceInfoManager.h"
#import "Constants.h"
#import "GlobalDataManager.h"
#import "ThirdPartySDKManager.h"
#import <Adjust/Adjust.h>

@interface APIClient ()

@property (nonatomic, strong) NetworkManager *networkManager;
@property (nonatomic, strong) DeviceInfoManager *deviceInfoManager;
@property (nonatomic, strong) GlobalDataManager *globalDataManager;
@property (nonatomic, strong) ThirdPartySDKManager *thirdPartySDKManager;

@end

@implementation APIClient

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static APIClient *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.networkManager = [NetworkManager sharedInstance];
        self.deviceInfoManager = [DeviceInfoManager sharedInstance];
        self.globalDataManager = [GlobalDataManager sharedInstance];
        self.thirdPartySDKManager = [ThirdPartySDKManager sharedInstance];
    }
    return self;
}

- (NSDictionary *)buildStandardHeaders {
    NSString *token = [self.globalDataManager authToken];
    return [self buildStandardHeadersWithToken:token];
}

- (NSDictionary *)buildStandardHeadersWithToken:(nullable NSString *)token {
    NSMutableDictionary *headers = [NSMutableDictionary dictionary];

    // 基础Headers
    headers[@"sec_ver"] = @"0";
    headers[@"platform"] = @"iOS";
    headers[@"is_anchor"] = @"false";

    // 设备和应用信息Headers
    headers[@"ver"] = self.deviceInfoManager.appVersion;
    headers[@"device-id"] = self.deviceInfoManager.deviceId;
    headers[@"model"] = self.deviceInfoManager.deviceModel;
    headers[@"lang"] = self.deviceInfoManager.languageCode;
    headers[@"sys_lan"] = self.deviceInfoManager.systemLanguage;
    headers[@"platform_ver"] = self.deviceInfoManager.systemVersion;
    headers[@"pkg"] = self.deviceInfoManager.bundleIdentifier;
    headers[@"device_lang"] = self.deviceInfoManager.systemLanguage;
    headers[@"device_country"] = self.deviceInfoManager.countryCode;
    headers[@"time_zone"] = self.deviceInfoManager.timeZone;

    // 添加Authorization token
    if (token && token.length > 0) {
        headers[@"Authorization"] = [NSString stringWithFormat:@"Bearer %@", token];
    }

    // 添加归因SDK信息
    headers[@"attribution_sdk"] = @"AJ";
    headers[@"attribution_sdk_ver"] = [Adjust sdkVersion] ?: @"";

    // 添加设备标识符
    headers[@"ad_device_id"] = [Adjust idfv] ?: @"";
    headers[@"ad_id"] = [Adjust adid] ?: @"";

    // 内部获取归因信息并调整key值
    NSDictionary *attribution = [self.thirdPartySDKManager getAdjustAttribution];
    if (attribution && attribution.count > 0) {
        headers[@"campaign_id"] = attribution[@"campaign"] ?: @"";
        headers[@"af_adset_id"] = attribution[@"creative"] ?: @"";
        headers[@"af_adgroup_id"] = attribution[@"adgroup"] ?: @"";
        headers[@"utm-source"] = attribution[@"network"] ?: @"";
    }

    return [headers copy];
}

#pragma mark - 私有方法
- (NSDictionary *)buildHeadersWithToken:(nullable NSString *)token {
    return [self buildStandardHeadersWithToken:token];
}

- (void)handleAPIResponse:(id)jsonObject error:(NSError *)error completion:(APICompletionBlock)completion {
    if (error) {
        if (completion) {
            completion(NO, nil, error);
        }
        return;
    }
    
    if (![jsonObject isKindOfClass:[NSDictionary class]]) {
        if (completion) {
            NSError *parseError = [NSError errorWithDomain:@"APIClientError"
                                                      code:-2001
                                                  userInfo:@{NSLocalizedDescriptionKey: @"响应格式错误"}];
            completion(NO, nil, parseError);
        }
        return;
    }
    
    NSDictionary *response = (NSDictionary *)jsonObject;
    NSNumber *code = response[@"code"];
    
    if (code && [code integerValue] == 0) {
        // 成功
        if (completion) {
            completion(YES, response[@"data"], nil);
        }
    } else {
        // 失败
        if (completion) {
            NSString *message = response[@"msg"] ?: @"请求失败";
            NSError *apiError = [NSError errorWithDomain:@"APIClientError"
                                                    code:[code integerValue]
                                                userInfo:@{NSLocalizedDescriptionKey: message}];
            completion(NO, response[@"data"], apiError);
        }
    }
}

#pragma mark - API接口实现

- (void)getAppConfigWithCompletion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants appConfigPath]];
    NSDictionary *headers = [self buildStandardHeaders];
    
    [self.networkManager POST:url
                   parameters:nil
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)loginWithDeviceId:(NSString *)deviceId completion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants oauthPath]];
    NSDictionary *headers = [self buildStandardHeaders];
    NSDictionary *parameters = @{@"token": deviceId ?: @"", @"oauthType": @4};
    
    [self.networkManager POST:url
                   parameters:parameters
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)getStrategyWithCompletion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants strategyPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    [self.networkManager POST:url
                   parameters:nil
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)createOrderWithGoodsCode:(NSString *)goodsCode
                          source:(nullable NSString *)source
                           entry:(nullable NSString *)entry
                      completion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants createOrderPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
    parameters[@"goodsCode"] = goodsCode ?: @"";
    parameters[@"payChannel"] = @"IAP";
    if (source) {
        parameters[@"source"] = source;
    }
    if (entry) {
        parameters[@"entry"] = entry;
    }
    
    [self.networkManager POST:url
                   parameters:[parameters copy]
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)consumeIAPWithOrderNo:(NSString *)orderNo
                      receipt:(NSString *)receipt
                transactionId:(NSString *)transactionId
                   completion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants consumeIAPPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    NSDictionary *parameters = @{
        @"orderNo": orderNo ?: @"",
        @"payload": receipt ?: @"",
        @"transactionId": transactionId ?: @"",
        @"type": @"1"
    };
    
    [self.networkManager POST:url
                   parameters:parameters
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)reportAttributionWithData:(NSDictionary *)attributionData completion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants recordReqsPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    [self.networkManager POST:url
                   parameters:attributionData
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)reportRiskControlWithData:(NSString *)encryptedData completion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants riskReportPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    NSDictionary *parameters = @{@"data": encryptedData ?: @""};
    
    [self.networkManager POST:url
                   parameters:parameters
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

- (void)getStatusWithCompletion:(APICompletionBlock)completion {
    NSString *url = [[Constants baseURL] stringByAppendingString:[Constants shStatusPath]];
    NSString *token = [self.globalDataManager authToken];
    NSDictionary *headers = [self buildHeadersWithToken:token];
    
    [self.networkManager POST:url
                   parameters:nil
                      headers:headers
               JSONCompletion:^(id jsonObject, NSError *error) {
        [self handleAPIResponse:jsonObject error:error completion:completion];
    }];
}

@end
