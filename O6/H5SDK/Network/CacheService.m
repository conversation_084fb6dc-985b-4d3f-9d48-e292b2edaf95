//
//  CacheService.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "CacheService.h"
#import "Constants.h"

#pragma mark - 内存缓存策略实现

@interface MemoryCacheStrategy ()

@property (nonatomic, strong) NSCache *cache;

@end

@implementation MemoryCacheStrategy

- (instancetype)initWithCapacity:(NSUInteger)capacity {
    if (self = [super init]) {
        self.cache = [[NSCache alloc] init];
        self.cache.countLimit = capacity;
    }
    return self;
}

- (instancetype)init {
    return [self initWithCapacity:100]; // 默认容量100
}

- (void)setObject:(id)object forKey:(NSString *)key {
    if (object && key) {
        [self.cache setObject:object forKey:key];
    }
}

- (nullable id)objectForKey:(NSString *)key {
    if (!key) return nil;
    return [self.cache objectForKey:key];
}

- (void)removeObjectForKey:(NSString *)key {
    if (key) {
        [self.cache removeObjectForKey:key];
    }
}

- (void)removeAllObjects {
    [self.cache removeAllObjects];
}

- (BOOL)containsObjectForKey:(NSString *)key {
    return [self objectForKey:key] != nil;
}

@end

#pragma mark - 磁盘缓存策略实现

@interface DiskCacheStrategy ()

@property (nonatomic, strong) NSString *cacheDirectory;
@property (nonatomic, strong) NSFileManager *fileManager;
@property (nonatomic, strong) dispatch_queue_t diskQueue;

@end

@implementation DiskCacheStrategy

- (instancetype)initWithCacheDirectory:(NSString *)directory {
    if (self = [super init]) {
        self.cacheDirectory = directory;
        self.fileManager = [NSFileManager defaultManager];
        self.diskQueue = dispatch_queue_create([Constants diskCacheQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
        
        // 创建缓存目录
        [self createCacheDirectoryIfNeeded];
    }
    return self;
}

- (instancetype)init {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachesDirectory = [paths firstObject];
    NSString *cacheDirectory = [cachesDirectory stringByAppendingPathComponent:@"H5SDKCache"];
    return [self initWithCacheDirectory:cacheDirectory];
}

- (void)createCacheDirectoryIfNeeded {
    if (![self.fileManager fileExistsAtPath:self.cacheDirectory]) {
        NSError *error;
        [self.fileManager createDirectoryAtPath:self.cacheDirectory 
                    withIntermediateDirectories:YES 
                                     attributes:nil 
                                          error:&error];
        if (error) {
            NSLog(@"创建缓存目录失败: %@", error.localizedDescription);
        }
    }
}

- (NSString *)filePathForKey:(NSString *)key {
    NSString *fileName = [self safeFileNameFromKey:key];
    return [self.cacheDirectory stringByAppendingPathComponent:fileName];
}

- (NSString *)safeFileNameFromKey:(NSString *)key {
    // 将key转换为安全的文件名
    NSString *safeKey = [key stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    safeKey = [safeKey stringByReplacingOccurrencesOfString:@":" withString:@"_"];
    return [safeKey stringByAppendingString:@".cache"];
}

- (void)setObject:(id)object forKey:(NSString *)key {
    if (!object || !key) return;
    
    dispatch_async(self.diskQueue, ^{
        NSString *filePath = [self filePathForKey:key];
        NSData *data = [NSKeyedArchiver archivedDataWithRootObject:object requiringSecureCoding:NO error:nil];
        if (data) {
            [data writeToFile:filePath atomically:YES];
        }
    });
}

- (nullable id)objectForKey:(NSString *)key {
    if (!key) return nil;
    
    __block id object = nil;
    dispatch_sync(self.diskQueue, ^{
        NSString *filePath = [self filePathForKey:key];
        if ([self.fileManager fileExistsAtPath:filePath]) {
            NSData *data = [NSData dataWithContentsOfFile:filePath];
            if (data) {
                NSError *error;
                object = [NSKeyedUnarchiver unarchivedObjectOfClass:[NSObject class] fromData:data error:&error];
                if (error) {
                    NSLog(@"反序列化缓存对象失败: %@", error.localizedDescription);
                }
            }
        }
    });
    
    return object;
}

- (void)removeObjectForKey:(NSString *)key {
    if (!key) return;
    
    dispatch_async(self.diskQueue, ^{
        NSString *filePath = [self filePathForKey:key];
        if ([self.fileManager fileExistsAtPath:filePath]) {
            NSError *error;
            [self.fileManager removeItemAtPath:filePath error:&error];
            if (error) {
                NSLog(@"删除缓存文件失败: %@", error.localizedDescription);
            }
        }
    });
}

- (void)removeAllObjects {
    dispatch_async(self.diskQueue, ^{
        NSError *error;
        NSArray *files = [self.fileManager contentsOfDirectoryAtPath:self.cacheDirectory error:&error];
        if (!error) {
            for (NSString *file in files) {
                NSString *filePath = [self.cacheDirectory stringByAppendingPathComponent:file];
                [self.fileManager removeItemAtPath:filePath error:nil];
            }
        }
    });
}

- (BOOL)containsObjectForKey:(NSString *)key {
    if (!key) return NO;
    
    NSString *filePath = [self filePathForKey:key];
    return [self.fileManager fileExistsAtPath:filePath];
}

@end

#pragma mark - 混合缓存策略实现

@interface HybridCacheStrategy ()

@property (nonatomic, strong) MemoryCacheStrategy *memoryCache;
@property (nonatomic, strong) DiskCacheStrategy *diskCache;

@end

@implementation HybridCacheStrategy

- (instancetype)initWithMemoryCapacity:(NSUInteger)memoryCapacity 
                        cacheDirectory:(NSString *)directory {
    if (self = [super init]) {
        self.memoryCache = [[MemoryCacheStrategy alloc] initWithCapacity:memoryCapacity];
        self.diskCache = [[DiskCacheStrategy alloc] initWithCacheDirectory:directory];
    }
    return self;
}

- (instancetype)init {
    return [self initWithMemoryCapacity:50 cacheDirectory:nil];
}

- (void)setObject:(id)object forKey:(NSString *)key {
    [self.memoryCache setObject:object forKey:key];
    [self.diskCache setObject:object forKey:key];
}

- (nullable id)objectForKey:(NSString *)key {
    // 先从内存缓存获取
    id object = [self.memoryCache objectForKey:key];
    if (object) {
        return object;
    }
    
    // 从磁盘缓存获取并放入内存缓存
    object = [self.diskCache objectForKey:key];
    if (object) {
        [self.memoryCache setObject:object forKey:key];
    }
    
    return object;
}

- (void)removeObjectForKey:(NSString *)key {
    [self.memoryCache removeObjectForKey:key];
    [self.diskCache removeObjectForKey:key];
}

- (void)removeAllObjects {
    [self.memoryCache removeAllObjects];
    [self.diskCache removeAllObjects];
}

- (BOOL)containsObjectForKey:(NSString *)key {
    return [self.memoryCache containsObjectForKey:key] || [self.diskCache containsObjectForKey:key];
}

@end

#pragma mark - 缓存服务主类实现

@interface CacheService ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDate *> *expirationTimes;
@property (nonatomic, strong) dispatch_queue_t cacheQueue;

@end

@implementation CacheService

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static CacheService *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        // 默认使用混合缓存策略
        self.cacheStrategy = [[HybridCacheStrategy alloc] init];
        self.expirationTimes = [NSMutableDictionary dictionary];
        self.cacheQueue = dispatch_queue_create([Constants cacheQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);

        // 启动定期清理过期缓存
        [self startPeriodicCleanup];
    }
    return self;
}

#pragma mark - 缓存操作

- (void)setObject:(id)object forKey:(NSString *)key {
    [self setObject:object forKey:key withExpiration:0]; // 0表示不过期
}

- (void)setObject:(id)object forKey:(NSString *)key withExpiration:(NSTimeInterval)expiration {
    if (!object || !key || !self.cacheStrategy) return;

    dispatch_async(self.cacheQueue, ^{
        [self.cacheStrategy setObject:object forKey:key];

        if (expiration > 0) {
            NSDate *expirationDate = [NSDate dateWithTimeIntervalSinceNow:expiration];
            self.expirationTimes[key] = expirationDate;
        } else {
            [self.expirationTimes removeObjectForKey:key];
        }
    });
}

- (nullable id)objectForKey:(NSString *)key {
    if (!key || !self.cacheStrategy) return nil;

    __block id object = nil;
    dispatch_sync(self.cacheQueue, ^{
        // 检查是否过期
        if ([self isExpiredForKey:key]) {
            [self removeObjectForKey:key];
            return;
        }

        object = [self.cacheStrategy objectForKey:key];
    });

    return object;
}

- (void)removeObjectForKey:(NSString *)key {
    if (!key || !self.cacheStrategy) return;

    dispatch_async(self.cacheQueue, ^{
        [self.cacheStrategy removeObjectForKey:key];
        [self.expirationTimes removeObjectForKey:key];
    });
}

- (void)removeAllObjects {
    if (!self.cacheStrategy) return;

    dispatch_async(self.cacheQueue, ^{
        [self.cacheStrategy removeAllObjects];
        [self.expirationTimes removeAllObjects];
    });
}

- (BOOL)containsObjectForKey:(NSString *)key {
    if (!key || !self.cacheStrategy) return NO;

    __block BOOL contains = NO;
    dispatch_sync(self.cacheQueue, ^{
        if ([self isExpiredForKey:key]) {
            [self removeObjectForKey:key];
            contains = NO;
        } else {
            contains = [self.cacheStrategy containsObjectForKey:key];
        }
    });

    return contains;
}

#pragma mark - 过期检查

- (BOOL)isExpiredForKey:(NSString *)key {
    if (!key) return YES;

    NSDate *expirationDate = self.expirationTimes[key];
    if (!expirationDate) return NO; // 没有设置过期时间，不过期

    return [[NSDate date] compare:expirationDate] == NSOrderedDescending;
}

- (void)removeExpiredObjects {
    dispatch_async(self.cacheQueue, ^{
        NSArray *keys = [self.expirationTimes.allKeys copy];
        for (NSString *key in keys) {
            if ([self isExpiredForKey:key]) {
                [self.cacheStrategy removeObjectForKey:key];
                [self.expirationTimes removeObjectForKey:key];
            }
        }
    });
}

#pragma mark - 缓存统计

- (NSUInteger)cacheSize {
    __block NSUInteger size = 0;
    dispatch_sync(self.cacheQueue, ^{
        size = self.expirationTimes.count;
    });
    return size;
}

- (void)clearExpiredCache {
    [self removeExpiredObjects];
}

#pragma mark - 定期清理

- (void)startPeriodicCleanup {
    // 每5分钟清理一次过期缓存
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_LOW, 0), ^{
        while (YES) {
            sleep(300); // 5分钟
            [self removeExpiredObjects];
        }
    });
}

@end
