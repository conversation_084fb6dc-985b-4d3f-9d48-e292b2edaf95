//
//  APIClient.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// API请求完成回调
typedef void(^APICompletionBlock)(BOOL success, id _Nullable data, NSError * _Nullable error);

@interface APIClient : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;


- (NSDictionary *)buildStandardHeaders;

- (NSDictionary *)buildStandardHeadersWithToken:(nullable NSString *)token;

// 1. 应用配置接口
- (void)getAppConfigWithCompletion:(APICompletionBlock)completion;

// 2. 登录接口
- (void)loginWithDeviceId:(NSString *)deviceId 
               completion:(APICompletionBlock)completion;

// 3. 策略接口
- (void)getStrategyWithCompletion:(APICompletionBlock)completion;

// 4. 创建订单接口
- (void)createOrderWithGoodsCode:(NSString *)goodsCode 
                          source:(nullable NSString *)source
                           entry:(nullable NSString *)entry
                      completion:(APICompletionBlock)completion;

// 5. 消费IAP接口
- (void)consumeIAPWithOrderNo:(NSString *)orderNo 
                      receipt:(NSString *)receipt 
                transactionId:(NSString *)transactionId 
                   completion:(APICompletionBlock)completion;

// 6. 归因上报接口
- (void)reportAttributionWithData:(NSDictionary *)attributionData 
                       completion:(APICompletionBlock)completion;

// 7. 风控上报接口
- (void)reportRiskControlWithData:(NSString *)encryptedData 
                       completion:(APICompletionBlock)completion;

// 8. 获取状态接口
- (void)getStatusWithCompletion:(APICompletionBlock)completion;

@end

NS_ASSUME_NONNULL_END
