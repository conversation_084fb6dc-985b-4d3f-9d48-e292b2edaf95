//
//  DeviceInfoManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DeviceInfoManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 基础设备信息
@property (nonatomic, readonly) NSString *appVersion;
@property (nonatomic, readonly) NSString *deviceId;
@property (nonatomic, readonly) NSString *deviceModel;
@property (nonatomic, readonly) NSString *languageCode;
@property (nonatomic, readonly) NSString *systemLanguage;
@property (nonatomic, readonly) NSString *systemVersion;
@property (nonatomic, readonly) NSString *bundleIdentifier;
@property (nonatomic, readonly) NSString *countryCode;
@property (nonatomic, readonly) NSString *timeZone;

// 安全检测
@property (nonatomic, readonly) BOOL isVPNEnabled;
@property (nonatomic, readonly) BOOL isProxyEnabled;
@property (nonatomic, readonly) BOOL isJailbroken;
@property (nonatomic, readonly) BOOL isSimulator;

// 刷新设备信息缓存
- (void)refreshDeviceInfo;

@end

NS_ASSUME_NONNULL_END
