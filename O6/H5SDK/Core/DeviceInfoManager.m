//
//  DeviceInfoManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "DeviceInfoManager.h"
#import "KeychainManager.h"
#import <UIKit/UIKit.h>
#import <sys/utsname.h>
#import <ifaddrs.h>
#import <arpa/inet.h>

@interface DeviceInfoManager ()

@property (nonatomic, strong) NSString *cachedAppVersion;
@property (nonatomic, strong) NSString *cachedDeviceId;
@property (nonatomic, strong) NSString *cachedDeviceModel;
@property (nonatomic, strong) NSString *cachedLanguageCode;
@property (nonatomic, strong) NSString *cachedSystemLanguage;
@property (nonatomic, strong) NSString *cachedSystemVersion;
@property (nonatomic, strong) NSString *cachedBundleIdentifier;
@property (nonatomic, strong) NSString *cachedCountryCode;
@property (nonatomic, strong) NSString *cachedTimeZone;

@end

@implementation DeviceInfoManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static DeviceInfoManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
        [instance refreshDeviceInfo];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        [self refreshDeviceInfo];
    }
    return self;
}

#pragma mark - 公共方法

- (void)refreshDeviceInfo {
    [self loadBasicDeviceInfo];
}

#pragma mark - 私有方法

- (void)loadBasicDeviceInfo {
    // 获取应用版本
    NSDictionary *infoDictionary = [[NSBundle mainBundle] infoDictionary];
    self.cachedAppVersion = infoDictionary[@"CFBundleShortVersionString"] ?: @"1.0.0";
    
    // 获取Bundle ID
    self.cachedBundleIdentifier = infoDictionary[@"CFBundleIdentifier"] ?: @"";
    
    // 获取设备ID (从Keychain获取或生成)
    self.cachedDeviceId = [[KeychainManager sharedInstance] getDeviceId];
    
    // 获取设备型号
    struct utsname systemInfo;
    uname(&systemInfo);
    self.cachedDeviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    
    // 获取系统版本
    self.cachedSystemVersion = [[UIDevice currentDevice] systemVersion];
    
    // 获取语言设置
    NSArray *languages = [NSLocale preferredLanguages];
    self.cachedLanguageCode = languages.firstObject ?: @"en";
    self.cachedSystemLanguage = [[NSLocale currentLocale] localeIdentifier];
    
    // 获取国家代码
    self.cachedCountryCode = [[NSLocale currentLocale] countryCode] ?: @"US";
    
    // 获取时区
    NSTimeZone *timeZone = [NSTimeZone systemTimeZone];
    self.cachedTimeZone = timeZone.name;
}

#pragma mark - 属性访问器

- (NSString *)appVersion {
    return self.cachedAppVersion ?: @"1.0.0";
}

- (NSString *)deviceId {
    return self.cachedDeviceId ?: @"";
}

- (NSString *)deviceModel {
    return self.cachedDeviceModel ?: @"iPhone";
}

- (NSString *)languageCode {
    return self.cachedLanguageCode ?: @"en";
}

- (NSString *)systemLanguage {
    return self.cachedSystemLanguage ?: @"en_US";
}

- (NSString *)systemVersion {
    return self.cachedSystemVersion ?: @"12.0";
}

- (NSString *)bundleIdentifier {
    return self.cachedBundleIdentifier ?: @"";
}

- (NSString *)countryCode {
    return self.cachedCountryCode ?: @"US";
}

- (NSString *)timeZone {
    return self.cachedTimeZone ?: @"UTC";
}

#pragma mark - 安全检测

- (BOOL)isVPNEnabled {
    // 检测VPN连接
    NSDictionary *proxySettings = (__bridge NSDictionary *)CFNetworkCopySystemProxySettings();
    NSArray *proxies = (__bridge NSArray *)CFNetworkCopyProxiesForURL((__bridge CFURLRef)[NSURL URLWithString:@"http://www.google.com"], (__bridge CFDictionaryRef)proxySettings);
    
    for (NSDictionary *proxy in proxies) {
        NSString *proxyType = proxy[(__bridge NSString *)kCFProxyTypeKey];
        if ([proxyType isEqualToString:(__bridge NSString *)kCFProxyTypeHTTP] ||
            [proxyType isEqualToString:(__bridge NSString *)kCFProxyTypeHTTPS] ||
            [proxyType isEqualToString:(__bridge NSString *)kCFProxyTypeSOCKS]) {
            return YES;
        }
    }
    return NO;
}

- (BOOL)isProxyEnabled {
    // 检测代理设置
    CFDictionaryRef proxySettings = CFNetworkCopySystemProxySettings();
    NSDictionary *settings = (__bridge NSDictionary *)proxySettings;

    BOOL httpProxyEnabled = [settings[(__bridge NSString *)kCFNetworkProxiesHTTPEnable] boolValue];
    // 注意：kCFNetworkProxiesHTTPSEnable在iOS上不可用，所以只检查HTTP代理

    if (proxySettings) {
        CFRelease(proxySettings);
    }

    return httpProxyEnabled;
}

- (BOOL)isJailbroken {
    // 检测越狱
    NSArray *jailbreakPaths = @[
        @"/Applications/Cydia.app",
        @"/Library/MobileSubstrate/MobileSubstrate.dylib",
        @"/bin/bash",
        @"/usr/sbin/sshd",
        @"/etc/apt",
        @"/private/var/lib/apt/"
    ];
    
    for (NSString *path in jailbreakPaths) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
            return YES;
        }
    }
    
    // 尝试写入系统目录
    NSString *testPath = @"/private/test_jailbreak.txt";
    NSError *error;
    [@"test" writeToFile:testPath atomically:YES encoding:NSUTF8StringEncoding error:&error];
    if (!error) {
        [[NSFileManager defaultManager] removeItemAtPath:testPath error:nil];
        return YES;
    }
    
    return NO;
}

- (BOOL)isSimulator {
    // 检测模拟器
#if TARGET_IPHONE_SIMULATOR
    return YES;
#else
    return NO;
#endif
}

@end
