//
//  CryptoUtils.h
//  H5SDK
//
//  Created by <PERSON>5SD<PERSON> on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 加密策略协议
@protocol EncryptionStrategy <NSObject>

- (nullable NSString *)encrypt:(NSString *)plainText withKey:(NSString *)key;
- (nullable NSString *)decrypt:(NSString *)cipherText withKey:(NSString *)key;

@end

// AES加密策略实现
@interface AESEncryptionStrategy : NSObject <EncryptionStrategy>
@end

// Base64编码策略实现
@interface Base64EncryptionStrategy : NSObject <EncryptionStrategy>
@end

// 加密工具上下文类
@interface CryptoUtils : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 策略设置
@property (nonatomic, strong) id<EncryptionStrategy> encryptionStrategy;

// 加密解密方法
- (nullable NSString *)encryptData:(NSString *)data withKey:(NSString *)key;
- (nullable NSString *)decryptData:(NSString *)data withKey:(NSString *)key;

// 便捷方法 - 使用AES加密
- (nullable NSString *)aesEncrypt:(NSString *)plainText withKey:(NSString *)key;
- (nullable NSString *)aesDecrypt:(NSString *)cipherText withKey:(NSString *)key;

// 风控数据专用加密方法
- (nullable NSString *)encryptRiskControlData:(NSDictionary *)riskData withKey:(NSString *)key;

@end

NS_ASSUME_NONNULL_END
