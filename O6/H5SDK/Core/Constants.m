//
//  Constants.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "Constants.h"

@implementation Constants

// Bundle and App Configuration
+ (NSString *)bundleIdPrefix {
    return @"com.example.";
}

+ (NSString *)keychainService {
    return [self.bundleIdPrefix stringByAppendingString:@"keychain"];
}

+ (NSString *)appleId {
    return @"6746118609";
}

+ (NSString *)sdkVersion {
    return @"2.1.5";
}

// Server URLs
+ (NSString *)baseURL {
    return @"https://test-app.ather.club";
}

+ (NSString *)logURL {
    return @"https://test-log.ather.club";
}

+ (NSString *)imURL {
    return @"http://test-im.ather.club";
}

+ (NSString *)serviceURL {
    return @"https://app-h5.ather.club/UserAgreement.html";
}

+ (NSString *)policyURL {
    return @"https://app-h5.ather.club/PrivacyPolicy.html";
}

// Network Keys
+ (NSString *)key1 {
    return @"Intelligence";
}

+ (NSString *)key2 {
    return @"Relaxed";
}

+ (NSString *)key3 {
    return @"Quantitative";
}

+ (NSString *)key4 {
    return @"Printed";
}

// API Paths
+ (NSString *)appConfigPath {
    return @"/config/getAppConfigPostV2";
}

+ (NSString *)oauthPath {
    return @"/security/oauth";
}

+ (NSString *)strategyPath {
    return @"/config/getStrategyPostV2";
}

+ (NSString *)createOrderPath {
    return @"/coin/recharge/create";
}

+ (NSString *)consumeIAPPath {
    return @"/coin/recharge/payment/ipa";
}

+ (NSString *)recordReqsPath {
    return @"/hit/ascribeRecordReqs";
}

+ (NSString *)riskReportPath {
    return @"/risk/info/upload";
}

+ (NSString *)shStatusPath {
    return @"/config/sh";
}

+ (NSString *)logRecordPath {
    return @"/log/liveChatPostV2";
}

// Adjust Configuration
+ (NSString *)adjustAppToken {
    return @"lgvu8ln3o45c";
}

+ (NSString *)adjustOrderEvent {
    return @"4dnrmi";
}

+ (NSString *)adjustPurchaseEvent {
    return @"l0ramk";
}

+ (NSString *)adjustLoginEvent {
    return @"";
}

+ (NSString *)adjustRegisterEvent {
    return @"";
}

// Facebook Configuration
+ (NSString *)facebookAppId {
    return @"502290152376861"; // 需要替换为真实的Facebook App ID
}

// UI Resources
+ (NSString *)launchImageName {
    return @"launch";
}

// Error Domains
+ (NSString *)sdkErrorDomain {
    return @"com.h5sdk.error";
}

+ (NSString *)networkErrorDomain {
    return @"com.h5sdk.network.error";
}

+ (NSString *)paymentErrorDomain {
    return @"com.h5sdk.payment.error";
}

+ (NSString *)initializationErrorDomain {
    return @"com.h5sdk.initialization.error";
}

// Queue Names
+ (NSString *)thirdPartyQueueName {
    return @"com.h5sdk.thirdparty";
}

+ (NSString *)initializationQueueName {
    return @"com.h5sdk.initialization";
}

+ (NSString *)paymentQueueName {
    return @"com.h5sdk.payment";
}

+ (NSString *)globalDataQueueName {
    return @"com.h5sdk.globaldata";
}

+ (NSString *)networkQueueName {
    return @"com.h5sdk.network";
}

+ (NSString *)keychainQueueName {
    return @"com.h5sdk.keychain";
}

+ (NSString *)storageQueueName {
    return @"com.h5sdk.storage";
}

+ (NSString *)diskCacheQueueName {
    return @"com.h5sdk.disk.cache";
}

+ (NSString *)cacheQueueName {
    return @"com.h5sdk.cache";
}

// UserDefaults Keys
+ (NSString *)appConfigDataKey {
    return @"h5sdk_app_config_data";
}

+ (NSString *)authDataKey {
    return @"h5sdk_auth_data";
}

+ (NSString *)strategyDataKey {
    return @"h5sdk_strategy_data";
}

+ (NSString *)timestampSuffix {
    return @"_timestamp";
}

// Keychain Keys
+ (NSString *)deviceIdKey {
    return @"device_id";
}

// Example Product IDs
+ (NSString *)exampleProductId {
    return @"com.h5sdk.coins.100";
}

@end
