//
//  KeychainManager.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface KeychainManager : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 设备ID管理
- (NSString *)getDeviceId;
- (BOOL)saveDeviceId:(NSString *)deviceId;

// 通用Keychain操作
- (nullable NSString *)getStringForKey:(NSString *)key;
- (BOOL)setString:(NSString *)value forKey:(NSString *)key;
- (nullable NSData *)getDataForKey:(NSString *)key;
- (BOOL)setData:(NSData *)data forKey:(NSString *)key;
- (BOOL)deleteItemForKey:(NSString *)key;
- (BOOL)deleteAllItems;

@end

NS_ASSUME_NONNULL_END
