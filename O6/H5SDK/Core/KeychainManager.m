//
//  KeychainManager.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "KeychainManager.h"
#import "Constants.h"
#import <Security/Security.h>

// 设备ID键名已迁移到Constants中

@interface KeychainManager ()

@property (nonatomic, strong) NSString *service;
@property (nonatomic, strong) dispatch_queue_t keychainQueue;

@end

@implementation KeychainManager

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static KeychainManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.service = [Constants keychainService];
        self.keychainQueue = dispatch_queue_create([Constants keychainQueueName].UTF8String, DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

#pragma mark - 设备ID管理

- (NSString *)getDeviceId {
    __block NSString *deviceId = nil;
    dispatch_sync(self.keychainQueue, ^{
        deviceId = [self getStringForKey:[Constants deviceIdKey]];
        if (!deviceId || deviceId.length == 0) {
            // 生成新的设备ID
            deviceId = [[NSUUID UUID] UUIDString];
            [self setString:deviceId forKey:[Constants deviceIdKey]];
        }
    });
    return deviceId ?: [[NSUUID UUID] UUIDString];
}

- (BOOL)saveDeviceId:(NSString *)deviceId {
    if (!deviceId || deviceId.length == 0) {
        return NO;
    }

    __block BOOL success = NO;
    dispatch_sync(self.keychainQueue, ^{
        success = [self setString:deviceId forKey:[Constants deviceIdKey]];
    });
    return success;
}

#pragma mark - 通用Keychain操作

- (nullable NSString *)getStringForKey:(NSString *)key {
    NSData *data = [self getDataForKey:key];
    if (data) {
        return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    }
    return nil;
}

- (BOOL)setString:(NSString *)value forKey:(NSString *)key {
    if (!value) {
        return [self deleteItemForKey:key];
    }
    
    NSData *data = [value dataUsingEncoding:NSUTF8StringEncoding];
    return [self setData:data forKey:key];
}

- (nullable NSData *)getDataForKey:(NSString *)key {
    if (!key || key.length == 0) {
        return nil;
    }
    
    NSDictionary *query = @{
        (__bridge NSString *)kSecClass: (__bridge NSString *)kSecClassGenericPassword,
        (__bridge NSString *)kSecAttrService: self.service,
        (__bridge NSString *)kSecAttrAccount: key,
        (__bridge NSString *)kSecReturnData: @YES,
        (__bridge NSString *)kSecMatchLimit: (__bridge NSString *)kSecMatchLimitOne
    };
    
    CFTypeRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, &result);
    
    if (status == errSecSuccess && result) {
        NSData *data = (__bridge_transfer NSData *)result;
        return data;
    }
    
    return nil;
}

- (BOOL)setData:(NSData *)data forKey:(NSString *)key {
    if (!key || key.length == 0) {
        return NO;
    }
    
    // 先删除已存在的项
    [self deleteItemForKey:key];
    
    if (!data) {
        return YES; // 删除成功
    }
    
    NSDictionary *attributes = @{
        (__bridge NSString *)kSecClass: (__bridge NSString *)kSecClassGenericPassword,
        (__bridge NSString *)kSecAttrService: self.service,
        (__bridge NSString *)kSecAttrAccount: key,
        (__bridge NSString *)kSecValueData: data,
        (__bridge NSString *)kSecAttrAccessible: (__bridge NSString *)kSecAttrAccessibleWhenUnlockedThisDeviceOnly
    };
    
    OSStatus status = SecItemAdd((__bridge CFDictionaryRef)attributes, NULL);
    return status == errSecSuccess;
}

- (BOOL)deleteItemForKey:(NSString *)key {
    if (!key || key.length == 0) {
        return NO;
    }
    
    NSDictionary *query = @{
        (__bridge NSString *)kSecClass: (__bridge NSString *)kSecClassGenericPassword,
        (__bridge NSString *)kSecAttrService: self.service,
        (__bridge NSString *)kSecAttrAccount: key
    };
    
    OSStatus status = SecItemDelete((__bridge CFDictionaryRef)query);
    return status == errSecSuccess || status == errSecItemNotFound;
}

- (BOOL)deleteAllItems {
    NSDictionary *query = @{
        (__bridge NSString *)kSecClass: (__bridge NSString *)kSecClassGenericPassword,
        (__bridge NSString *)kSecAttrService: self.service
    };
    
    OSStatus status = SecItemDelete((__bridge CFDictionaryRef)query);
    return status == errSecSuccess || status == errSecItemNotFound;
}

@end
