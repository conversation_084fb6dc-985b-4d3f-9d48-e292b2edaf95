//
//  CryptoUtils.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "CryptoUtils.h"
#import <CommonCrypto/CommonCrypto.h>

#pragma mark - AES加密策略实现

@implementation AESEncryptionStrategy

- (nullable NSString *)encrypt:(NSString *)plainText withKey:(NSString *)key {
    if (!plainText || !key) return nil;
    
    NSData *plainData = [plainText dataUsingEncoding:NSUTF8StringEncoding];
    NSData *keyData = [self keyDataFromString:key];
    
    if (!plainData || !keyData) return nil;
    
    NSData *encryptedData = [self aesEncryptData:plainData withKey:keyData];
    if (!encryptedData) return nil;
    
    return [encryptedData base64EncodedStringWithOptions:0];
}

- (nullable NSString *)decrypt:(NSString *)cipherText withKey:(NSString *)key {
    if (!cipherText || !key) return nil;
    
    NSData *encryptedData = [[NSData alloc] initWithBase64EncodedString:cipherText options:0];
    NSData *keyData = [self keyDataFromString:key];
    
    if (!encryptedData || !keyData) return nil;
    
    NSData *decryptedData = [self aesDecryptData:encryptedData withKey:keyData];
    if (!decryptedData) return nil;
    
    return [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
}

#pragma mark - 私有方法

- (NSData *)keyDataFromString:(NSString *)keyString {
    // 确保密钥长度为32字节 (AES-256)
    NSString *paddedKeyString = keyString;

    if (keyString.length < kCCKeySizeAES256) {
        // 字符串补0到32字节长度
        NSUInteger paddingLength = kCCKeySizeAES256 - keyString.length;
        NSString *padding = [@"" stringByPaddingToLength:paddingLength withString:@"0" startingAtIndex:0];
        paddedKeyString = [keyString stringByAppendingString:padding];
    } else if (keyString.length > kCCKeySizeAES256) {
        // 如果超过32字节，截取前32字节
        paddedKeyString = [keyString substringToIndex:kCCKeySizeAES256];
    }

    return [paddedKeyString dataUsingEncoding:NSUTF8StringEncoding];
}

- (nullable NSData *)aesEncryptData:(NSData *)data withKey:(NSData *)key {
    size_t bufferSize = data.length + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    
    size_t numBytesEncrypted = 0;
    CCCryptorStatus cryptStatus = CCCrypt(kCCEncrypt,
                                         kCCAlgorithmAES,
                                         kCCOptionECBMode | kCCOptionPKCS7Padding,
                                         key.bytes,
                                         key.length,
                                         NULL, // IV
                                         data.bytes,
                                         data.length,
                                         buffer,
                                         bufferSize,
                                         &numBytesEncrypted);
    
    if (cryptStatus == kCCSuccess) {
        NSData *encryptedData = [NSData dataWithBytes:buffer length:numBytesEncrypted];
        free(buffer);
        return encryptedData;
    }
    
    free(buffer);
    return nil;
}

- (nullable NSData *)aesDecryptData:(NSData *)data withKey:(NSData *)key {
    size_t bufferSize = data.length + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    
    size_t numBytesDecrypted = 0;
    CCCryptorStatus cryptStatus = CCCrypt(kCCDecrypt,
                                         kCCAlgorithmAES,
                                         kCCOptionECBMode | kCCOptionPKCS7Padding,
                                         key.bytes,
                                         key.length,
                                         NULL, // IV
                                         data.bytes,
                                         data.length,
                                         buffer,
                                         bufferSize,
                                         &numBytesDecrypted);
    
    if (cryptStatus == kCCSuccess) {
        NSData *decryptedData = [NSData dataWithBytes:buffer length:numBytesDecrypted];
        free(buffer);
        return decryptedData;
    }
    
    free(buffer);
    return nil;
}

@end

#pragma mark - Base64编码策略实现

@implementation Base64EncryptionStrategy

- (nullable NSString *)encrypt:(NSString *)plainText withKey:(NSString *)key {
    if (!plainText) return nil;
    
    NSData *data = [plainText dataUsingEncoding:NSUTF8StringEncoding];
    return [data base64EncodedStringWithOptions:0];
}

- (nullable NSString *)decrypt:(NSString *)cipherText withKey:(NSString *)key {
    if (!cipherText) return nil;
    
    NSData *data = [[NSData alloc] initWithBase64EncodedString:cipherText options:0];
    if (!data) return nil;
    
    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

@end

#pragma mark - 加密工具上下文类

@implementation CryptoUtils

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static CryptoUtils *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
        // 默认使用AES加密策略
        instance.encryptionStrategy = [[AESEncryptionStrategy alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.encryptionStrategy = [[AESEncryptionStrategy alloc] init];
    }
    return self;
}

#pragma mark - 加密解密方法

- (nullable NSString *)encryptData:(NSString *)data withKey:(NSString *)key {
    if (!self.encryptionStrategy) {
        return nil;
    }
    return [self.encryptionStrategy encrypt:data withKey:key];
}

- (nullable NSString *)decryptData:(NSString *)data withKey:(NSString *)key {
    if (!self.encryptionStrategy) {
        return nil;
    }
    return [self.encryptionStrategy decrypt:data withKey:key];
}

#pragma mark - 便捷方法

- (nullable NSString *)aesEncrypt:(NSString *)plainText withKey:(NSString *)key {
    AESEncryptionStrategy *aesStrategy = [[AESEncryptionStrategy alloc] init];
    return [aesStrategy encrypt:plainText withKey:key];
}

- (nullable NSString *)aesDecrypt:(NSString *)cipherText withKey:(NSString *)key {
    AESEncryptionStrategy *aesStrategy = [[AESEncryptionStrategy alloc] init];
    return [aesStrategy decrypt:cipherText withKey:key];
}

#pragma mark - 风控数据专用加密方法

- (nullable NSString *)encryptRiskControlData:(NSDictionary *)riskData withKey:(NSString *)key {
    if (!riskData || !key) return nil;
    
    // 将字典转换为JSON字符串
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:riskData 
                                                       options:0 
                                                         error:&error];
    if (error || !jsonData) {
        return nil;
    }
    
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    if (!jsonString) return nil;
    
    // 使用AES加密
    return [self aesEncrypt:jsonString withKey:key];
}

@end
