//
//  Constants.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface Constants : NSObject

// Bundle and App Configuration
@property (class, nonatomic, readonly) NSString *bundleIdPrefix;
@property (class, nonatomic, readonly) NSString *keychainService;
@property (class, nonatomic, readonly) NSString *appleId;
@property (class, nonatomic, readonly) NSString *sdkVersion;

// Server URLs
@property (class, nonatomic, readonly) NSString *baseURL;
@property (class, nonatomic, readonly) NSString *logURL;
@property (class, nonatomic, readonly) NSString *imURL;
@property (class, nonatomic, readonly) NSString *serviceURL;
@property (class, nonatomic, readonly) NSString *policyURL;

// Network Keys
@property (class, nonatomic, readonly) NSString *key1;
@property (class, nonatomic, readonly) NSString *key2;
@property (class, nonatomic, readonly) NSString *key3;
@property (class, nonatomic, readonly) NSString *key4;

// API Paths
@property (class, nonatomic, readonly) NSString *appConfigPath;
@property (class, nonatomic, readonly) NSString *oauthPath;
@property (class, nonatomic, readonly) NSString *strategyPath;
@property (class, nonatomic, readonly) NSString *createOrderPath;
@property (class, nonatomic, readonly) NSString *consumeIAPPath;
@property (class, nonatomic, readonly) NSString *recordReqsPath;
@property (class, nonatomic, readonly) NSString *riskReportPath;
@property (class, nonatomic, readonly) NSString *shStatusPath;
@property (class, nonatomic, readonly) NSString *logRecordPath;

// Adjust Configuration
@property (class, nonatomic, readonly) NSString *adjustAppToken;
@property (class, nonatomic, readonly) NSString *adjustOrderEvent;
@property (class, nonatomic, readonly) NSString *adjustPurchaseEvent;
@property (class, nonatomic, readonly) NSString *adjustLoginEvent;
@property (class, nonatomic, readonly) NSString *adjustRegisterEvent;

// Facebook Configuration
@property (class, nonatomic, readonly) NSString *facebookAppId;

// UI Resources
@property (class, nonatomic, readonly) NSString *launchImageName;

// Error Domains
@property (class, nonatomic, readonly) NSString *sdkErrorDomain;
@property (class, nonatomic, readonly) NSString *networkErrorDomain;
@property (class, nonatomic, readonly) NSString *paymentErrorDomain;
@property (class, nonatomic, readonly) NSString *initializationErrorDomain;

// Queue Names
@property (class, nonatomic, readonly) NSString *thirdPartyQueueName;
@property (class, nonatomic, readonly) NSString *initializationQueueName;
@property (class, nonatomic, readonly) NSString *paymentQueueName;
@property (class, nonatomic, readonly) NSString *globalDataQueueName;
@property (class, nonatomic, readonly) NSString *networkQueueName;
@property (class, nonatomic, readonly) NSString *keychainQueueName;
@property (class, nonatomic, readonly) NSString *storageQueueName;
@property (class, nonatomic, readonly) NSString *diskCacheQueueName;
@property (class, nonatomic, readonly) NSString *cacheQueueName;

// UserDefaults Keys
@property (class, nonatomic, readonly) NSString *appConfigDataKey;
@property (class, nonatomic, readonly) NSString *authDataKey;
@property (class, nonatomic, readonly) NSString *strategyDataKey;
@property (class, nonatomic, readonly) NSString *timestampSuffix;

// Keychain Keys
@property (class, nonatomic, readonly) NSString *deviceIdKey;

// Example Product IDs
@property (class, nonatomic, readonly) NSString *exampleProductId;

@end

NS_ASSUME_NONNULL_END
