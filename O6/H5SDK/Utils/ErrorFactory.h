//
//  ErrorFactory.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 错误域定义
extern NSString * const H5SDKErrorDomain;
extern NSString * const H5SDKNetworkErrorDomain;
extern NSString * const H5SDKPaymentErrorDomain;
extern NSString * const H5SDKInitializationErrorDomain;

// 错误码定义
typedef NS_ENUM(NSInteger, H5SDKErrorCode) {
    // 通用错误 (1000-1099)
    H5SDKErrorCodeUnknown = 1000,
    H5SDKErrorCodeInvalidParameter = 1001,
    H5SDKErrorCodeNotInitialized = 1002,
    H5SDKErrorCodeNotLoggedIn = 1003,
    H5SDKErrorCodeOperationCancelled = 1004,
    H5SDKErrorCodeOperationTimeout = 1005,
    
    // 网络错误 (2000-2099)
    H5SDKErrorCodeNetworkUnavailable = 2000,
    H5SDKErrorCodeNetworkTimeout = 2001,
    H5SDKErrorCodeInvalidURL = 2002,
    H5SDKErrorCodeRequestFailed = 2003,
    H5SDKErrorCodeResponseParsingFailed = 2004,
    H5SDKErrorCodeAPIError = 2005,
    
    // 支付错误 (3000-3099)
    H5SDKErrorCodePaymentNotSupported = 3000,
    H5SDKErrorCodePaymentCancelled = 3001,
    H5SDKErrorCodePaymentFailed = 3002,
    H5SDKErrorCodeProductNotFound = 3003,
    H5SDKErrorCodeReceiptValidationFailed = 3004,
    H5SDKErrorCodePaymentInProgress = 3005,
    
    // 初始化错误 (4000-4099)
    H5SDKErrorCodeInitializationFailed = 4000,
    H5SDKErrorCodeConfigurationInvalid = 4001,
    H5SDKErrorCodeThirdPartySDKFailed = 4002,
    H5SDKErrorCodeAuthenticationFailed = 4003,
    H5SDKErrorCodeStrategyValidationFailed = 4004,
    
    // 数据错误 (5000-5099)
    H5SDKErrorCodeDataCorrupted = 5000,
    H5SDKErrorCodeEncryptionFailed = 5001,
    H5SDKErrorCodeDecryptionFailed = 5002,
    H5SDKErrorCodeCacheError = 5003,
    H5SDKErrorCodeStorageError = 5004
};

@interface ErrorFactory : NSObject

// 通用错误创建
+ (NSError *)errorWithCode:(H5SDKErrorCode)code;
+ (NSError *)errorWithCode:(H5SDKErrorCode)code message:(NSString *)message;
+ (NSError *)errorWithCode:(H5SDKErrorCode)code message:(NSString *)message userInfo:(nullable NSDictionary *)userInfo;

// 网络错误创建
+ (NSError *)networkErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message;
+ (NSError *)networkTimeoutError;
+ (NSError *)networkUnavailableError;
+ (NSError *)invalidURLError:(NSString *)url;
+ (NSError *)requestFailedError:(NSError *)underlyingError;
+ (NSError *)responseParsingError:(NSError *)underlyingError;
+ (NSError *)apiError:(NSInteger)apiCode message:(NSString *)message;

// 支付错误创建
+ (NSError *)paymentErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message;
+ (NSError *)paymentNotSupportedError;
+ (NSError *)paymentCancelledError;
+ (NSError *)paymentFailedError:(NSError *)underlyingError;
+ (NSError *)productNotFoundError:(NSString *)productCode;
+ (NSError *)receiptValidationError;
+ (NSError *)paymentInProgressError;

// 初始化错误创建
+ (NSError *)initializationErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message;
+ (NSError *)configurationInvalidError:(NSArray<NSString *> *)validationErrors;
+ (NSError *)thirdPartySDKError:(NSString *)sdkName underlyingError:(NSError *)error;
+ (NSError *)authenticationFailedError:(NSError *)underlyingError;
+ (NSError *)strategyValidationError;

// 数据错误创建
+ (NSError *)dataErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message;
+ (NSError *)encryptionFailedError;
+ (NSError *)decryptionFailedError;
+ (NSError *)cacheError:(NSError *)underlyingError;
+ (NSError *)storageError:(NSError *)underlyingError;

// 错误信息本地化
+ (NSString *)localizedDescriptionForErrorCode:(H5SDKErrorCode)code;

@end

NS_ASSUME_NONNULL_END
