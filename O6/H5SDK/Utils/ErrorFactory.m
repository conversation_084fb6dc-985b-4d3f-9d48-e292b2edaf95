//
//  ErrorFactory.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "ErrorFactory.h"
#import "Constants.h"

// 错误域定义 - 使用Constants中的值
NSString * const H5SDKErrorDomain = @"com.h5sdk.error";
NSString * const H5SDKNetworkErrorDomain = @"com.h5sdk.network.error";
NSString * const H5SDKPaymentErrorDomain = @"com.h5sdk.payment.error";
NSString * const H5SDKInitializationErrorDomain = @"com.h5sdk.initialization.error";

@implementation ErrorFactory

#pragma mark - 通用错误创建

+ (NSError *)errorWithCode:(H5SDKErrorCode)code {
    NSString *message = [self localizedDescriptionForErrorCode:code];
    return [self errorWithCode:code message:message];
}

+ (NSError *)errorWithCode:(H5SDKErrorCode)code message:(NSString *)message {
    return [self errorWithCode:code message:message userInfo:nil];
}

+ (NSError *)errorWithCode:(H5SDKErrorCode)code message:(NSString *)message userInfo:(nullable NSDictionary *)userInfo {
    NSMutableDictionary *info = [NSMutableDictionary dictionary];
    if (message) {
        info[NSLocalizedDescriptionKey] = message;
    }
    if (userInfo) {
        [info addEntriesFromDictionary:userInfo];
    }
    
    return [NSError errorWithDomain:[Constants sdkErrorDomain] code:code userInfo:[info copy]];
}

#pragma mark - 网络错误创建

+ (NSError *)networkErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message {
    return [NSError errorWithDomain:[Constants networkErrorDomain]
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: message}];
}

+ (NSError *)networkTimeoutError {
    return [self networkErrorWithCode:H5SDKErrorCodeNetworkTimeout
                              message:@"Network request timeout"];
}

+ (NSError *)networkUnavailableError {
    return [self networkErrorWithCode:H5SDKErrorCodeNetworkUnavailable
                              message:@"Network connection unavailable"];
}

+ (NSError *)invalidURLError:(NSString *)url {
    NSString *message = [NSString stringWithFormat:@"Invalid URL: %@", url ?: @"(null)"];
    return [self networkErrorWithCode:H5SDKErrorCodeInvalidURL message:message];
}

+ (NSError *)requestFailedError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"Network request failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:[Constants networkErrorDomain]
                               code:H5SDKErrorCodeRequestFailed
                           userInfo:[userInfo copy]];
}

+ (NSError *)responseParsingError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"Response data parsing failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:[Constants networkErrorDomain]
                               code:H5SDKErrorCodeResponseParsingFailed
                           userInfo:[userInfo copy]];
}

+ (NSError *)apiError:(NSInteger)apiCode message:(NSString *)message {
    NSString *errorMessage = [NSString stringWithFormat:@"API error (code: %ld): %@",
                             (long)apiCode, message ?: @"Unknown error"];

    return [NSError errorWithDomain:[Constants networkErrorDomain]
                               code:H5SDKErrorCodeAPIError
                           userInfo:@{
                               NSLocalizedDescriptionKey: errorMessage,
                               @"APIErrorCode": @(apiCode)
                           }];
}

#pragma mark - 支付错误创建

+ (NSError *)paymentErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message {
    return [NSError errorWithDomain:[Constants paymentErrorDomain]
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: message}];
}

+ (NSError *)paymentNotSupportedError {
    return [self paymentErrorWithCode:H5SDKErrorCodePaymentNotSupported
                              message:@"Device does not support in-app purchases"];
}

+ (NSError *)paymentCancelledError {
    return [self paymentErrorWithCode:H5SDKErrorCodePaymentCancelled
                              message:@"User cancelled the payment"];
}

+ (NSError *)paymentFailedError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"Payment failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:[Constants paymentErrorDomain]
                               code:H5SDKErrorCodePaymentFailed
                           userInfo:[userInfo copy]];
}

+ (NSError *)productNotFoundError:(NSString *)productCode {
    NSString *message = [NSString stringWithFormat:@"Product not found: %@", productCode ?: @"(null)"];
    return [self paymentErrorWithCode:H5SDKErrorCodeProductNotFound message:message];
}

+ (NSError *)receiptValidationError {
    return [self paymentErrorWithCode:H5SDKErrorCodeReceiptValidationFailed
                              message:@"Purchase receipt validation failed"];
}

+ (NSError *)paymentInProgressError {
    return [self paymentErrorWithCode:H5SDKErrorCodePaymentInProgress
                              message:@"Payment is in progress"];
}

#pragma mark - 初始化错误创建

+ (NSError *)initializationErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message {
    return [NSError errorWithDomain:[Constants initializationErrorDomain]
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: message}];
}

+ (NSError *)configurationInvalidError:(NSArray<NSString *> *)validationErrors {
    NSString *message = @"SDK configuration invalid";
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = message;
    if (validationErrors && validationErrors.count > 0) {
        userInfo[@"ValidationErrors"] = validationErrors;
        message = [NSString stringWithFormat:@"%@: %@", message, [validationErrors componentsJoinedByString:@", "]];
        userInfo[NSLocalizedDescriptionKey] = message;
    }

    return [NSError errorWithDomain:[Constants initializationErrorDomain]
                               code:H5SDKErrorCodeConfigurationInvalid
                           userInfo:[userInfo copy]];
}

+ (NSError *)thirdPartySDKError:(NSString *)sdkName underlyingError:(NSError *)error {
    NSString *message = [NSString stringWithFormat:@"%@ SDK initialization failed", sdkName ?: @"Third-party"];
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = message;
    if (error) {
        userInfo[NSUnderlyingErrorKey] = error;
    }

    return [NSError errorWithDomain:[Constants initializationErrorDomain]
                               code:H5SDKErrorCodeThirdPartySDKFailed
                           userInfo:[userInfo copy]];
}

+ (NSError *)authenticationFailedError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"User authentication failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:[Constants initializationErrorDomain]
                               code:H5SDKErrorCodeAuthenticationFailed
                           userInfo:[userInfo copy]];
}

+ (NSError *)strategyValidationError {
    return [self initializationErrorWithCode:H5SDKErrorCodeStrategyValidationFailed
                                     message:@"Strategy data validation failed"];
}

#pragma mark - 数据错误创建

+ (NSError *)dataErrorWithCode:(H5SDKErrorCode)code message:(NSString *)message {
    return [NSError errorWithDomain:H5SDKErrorDomain
                               code:code
                           userInfo:@{NSLocalizedDescriptionKey: message}];
}

+ (NSError *)encryptionFailedError {
    return [self dataErrorWithCode:H5SDKErrorCodeEncryptionFailed
                           message:@"Data encryption failed"];
}

+ (NSError *)decryptionFailedError {
    return [self dataErrorWithCode:H5SDKErrorCodeDecryptionFailed
                           message:@"Data decryption failed"];
}

+ (NSError *)cacheError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"Cache operation failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:H5SDKErrorDomain
                               code:H5SDKErrorCodeCacheError
                           userInfo:[userInfo copy]];
}

+ (NSError *)storageError:(NSError *)underlyingError {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[NSLocalizedDescriptionKey] = @"Storage operation failed";
    if (underlyingError) {
        userInfo[NSUnderlyingErrorKey] = underlyingError;
    }

    return [NSError errorWithDomain:H5SDKErrorDomain
                               code:H5SDKErrorCodeStorageError
                           userInfo:[userInfo copy]];
}

#pragma mark - 错误信息本地化

+ (NSString *)localizedDescriptionForErrorCode:(H5SDKErrorCode)code {
    switch (code) {
        // 通用错误
        case H5SDKErrorCodeUnknown:
            return @"Unknown error";
        case H5SDKErrorCodeInvalidParameter:
            return @"Invalid parameter";
        case H5SDKErrorCodeNotInitialized:
            return @"SDK not initialized";
        case H5SDKErrorCodeNotLoggedIn:
            return @"User not logged in";
        case H5SDKErrorCodeOperationCancelled:
            return @"Operation cancelled";
        case H5SDKErrorCodeOperationTimeout:
            return @"Operation timeout";

        // 网络错误
        case H5SDKErrorCodeNetworkUnavailable:
            return @"Network connection unavailable";
        case H5SDKErrorCodeNetworkTimeout:
            return @"Network request timeout";
        case H5SDKErrorCodeInvalidURL:
            return @"Invalid URL";
        case H5SDKErrorCodeRequestFailed:
            return @"Network request failed";
        case H5SDKErrorCodeResponseParsingFailed:
            return @"Response data parsing failed";
        case H5SDKErrorCodeAPIError:
            return @"API interface error";

        // 支付错误
        case H5SDKErrorCodePaymentNotSupported:
            return @"Device does not support in-app purchases";
        case H5SDKErrorCodePaymentCancelled:
            return @"User cancelled the payment";
        case H5SDKErrorCodePaymentFailed:
            return @"Payment failed";
        case H5SDKErrorCodeProductNotFound:
            return @"Product not found";
        case H5SDKErrorCodeReceiptValidationFailed:
            return @"Purchase receipt validation failed";
        case H5SDKErrorCodePaymentInProgress:
            return @"Payment is in progress";

        // 初始化错误
        case H5SDKErrorCodeInitializationFailed:
            return @"SDK initialization failed";
        case H5SDKErrorCodeConfigurationInvalid:
            return @"SDK configuration invalid";
        case H5SDKErrorCodeThirdPartySDKFailed:
            return @"Third-party SDK initialization failed";
        case H5SDKErrorCodeAuthenticationFailed:
            return @"User authentication failed";
        case H5SDKErrorCodeStrategyValidationFailed:
            return @"Strategy data validation failed";

        // 数据错误
        case H5SDKErrorCodeDataCorrupted:
            return @"Data corrupted";
        case H5SDKErrorCodeEncryptionFailed:
            return @"Data encryption failed";
        case H5SDKErrorCodeDecryptionFailed:
            return @"Data decryption failed";
        case H5SDKErrorCodeCacheError:
            return @"Cache operation failed";
        case H5SDKErrorCodeStorageError:
            return @"Storage operation failed";

        default:
            return @"Unknown error";
    }
}

@end
