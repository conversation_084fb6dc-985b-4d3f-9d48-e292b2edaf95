//
//  Logger.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "Logger.h"

@interface Logger ()

@property (nonatomic, strong) NSString *logFilePath;
@property (nonatomic, strong) dispatch_queue_t logQueue;
@property (nonatomic, strong) NSMutableArray<NSString *> *logBuffer;
@property (nonatomic, strong) NSDateFormatter *dateFormatter;

@end

@implementation Logger

#pragma mark - 单例实现

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static Logger *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        self.logLevel = LogLevelInfo;
        self.fileLoggingEnabled = NO;
        self.logQueue = dispatch_queue_create("com.h5sdk.logger", DISPATCH_QUEUE_SERIAL);
        self.logBuffer = [NSMutableArray array];
        
        // 设置日期格式化器
        self.dateFormatter = [[NSDateFormatter alloc] init];
        self.dateFormatter.dateFormat = @"yyyy-MM-dd HH:mm:ss.SSS";
        
        // 设置日志文件路径
        [self setupLogFilePath];
    }
    return self;
}

- (void)setupLogFilePath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    NSString *documentsDirectory = [paths firstObject];
    self.logFilePath = [documentsDirectory stringByAppendingPathComponent:@"H5SDK.log"];
}

#pragma mark - 类方法

+ (void)verbose:(NSString *)format, ... {
    va_list args;
    va_start(args, format);
    [[self sharedInstance] logWithLevel:LogLevelVerbose format:format arguments:args];
    va_end(args);
}

+ (void)debug:(NSString *)format, ... {
    va_list args;
    va_start(args, format);
    [[self sharedInstance] logWithLevel:LogLevelDebug format:format arguments:args];
    va_end(args);
}

+ (void)info:(NSString *)format, ... {
    va_list args;
    va_start(args, format);
    [[self sharedInstance] logWithLevel:LogLevelInfo format:format arguments:args];
    va_end(args);
}

+ (void)warning:(NSString *)format, ... {
    va_list args;
    va_start(args, format);
    [[self sharedInstance] logWithLevel:LogLevelWarning format:format arguments:args];
    va_end(args);
}

+ (void)error:(NSString *)format, ... {
    va_list args;
    va_start(args, format);
    [[self sharedInstance] logWithLevel:LogLevelError format:format arguments:args];
    va_end(args);
}

#pragma mark - 实例方法

- (void)logWithLevel:(LogLevel)level format:(NSString *)format arguments:(va_list)arguments {
    if (level < self.logLevel) {
        return;
    }
    
    NSString *message = [[NSString alloc] initWithFormat:format arguments:arguments];
    [self logWithLevel:level message:message];
}

- (void)logWithLevel:(LogLevel)level message:(NSString *)message {
    if (level < self.logLevel) {
        return;
    }
    
    dispatch_async(self.logQueue, ^{
        NSString *timestamp = [self.dateFormatter stringFromDate:[NSDate date]];
        NSString *levelString = [self stringForLogLevel:level];
        NSString *logMessage = [NSString stringWithFormat:@"[%@] [%@] %@", timestamp, levelString, message];
        
        // 控制台输出
        NSLog(@"%@", logMessage);
        
        // 添加到缓冲区
        [self.logBuffer addObject:logMessage];
        
        // 限制缓冲区大小
        if (self.logBuffer.count > 1000) {
            [self.logBuffer removeObjectAtIndex:0];
        }
        
        // 文件输出
        if (self.fileLoggingEnabled) {
            [self writeToLogFile:logMessage];
        }
    });
}

- (NSString *)stringForLogLevel:(LogLevel)level {
    switch (level) {
        case LogLevelVerbose:
            return @"VERBOSE";
        case LogLevelDebug:
            return @"DEBUG";
        case LogLevelInfo:
            return @"INFO";
        case LogLevelWarning:
            return @"WARNING";
        case LogLevelError:
            return @"ERROR";
        case LogLevelNone:
            return @"NONE";
        default:
            return @"UNKNOWN";
    }
}

- (void)writeToLogFile:(NSString *)logMessage {
    NSString *logLine = [logMessage stringByAppendingString:@"\n"];
    NSData *data = [logLine dataUsingEncoding:NSUTF8StringEncoding];
    
    if (![[NSFileManager defaultManager] fileExistsAtPath:self.logFilePath]) {
        [[NSFileManager defaultManager] createFileAtPath:self.logFilePath contents:nil attributes:nil];
    }
    
    NSFileHandle *fileHandle = [NSFileHandle fileHandleForWritingAtPath:self.logFilePath];
    if (fileHandle) {
        [fileHandle seekToEndOfFile];
        [fileHandle writeData:data];
        [fileHandle closeFile];
    }
}

#pragma mark - 日志文件管理

- (void)clearLogFile {
    dispatch_async(self.logQueue, ^{
        [[NSFileManager defaultManager] removeItemAtPath:self.logFilePath error:nil];
        [self.logBuffer removeAllObjects];
    });
}

- (nullable NSString *)getLogFileContent {
    if (![[NSFileManager defaultManager] fileExistsAtPath:self.logFilePath]) {
        return nil;
    }
    
    NSError *error;
    NSString *content = [NSString stringWithContentsOfFile:self.logFilePath 
                                                   encoding:NSUTF8StringEncoding 
                                                      error:&error];
    if (error) {
        LogE(@"读取日志文件失败: %@", error.localizedDescription);
        return nil;
    }
    
    return content;
}

- (NSArray<NSString *> *)getRecentLogs:(NSUInteger)count {
    __block NSArray<NSString *> *recentLogs;
    dispatch_sync(self.logQueue, ^{
        NSUInteger startIndex = self.logBuffer.count > count ? self.logBuffer.count - count : 0;
        recentLogs = [self.logBuffer subarrayWithRange:NSMakeRange(startIndex, self.logBuffer.count - startIndex)];
    });
    return recentLogs;
}

#pragma mark - 日志上报

- (void)uploadLogsWithCompletion:(void(^)(BOOL success, NSError * _Nullable error))completion {
    dispatch_async(self.logQueue, ^{
        NSString *logContent = [self getLogFileContent];
        if (!logContent || logContent.length == 0) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (completion) {
                    NSError *error = [NSError errorWithDomain:@"LoggerError"
                                                         code:-1001
                                                     userInfo:@{NSLocalizedDescriptionKey: @"No log content to report"}];
                    completion(NO, error);
                }
            });
            return;
        }
        
        // 这里应该调用APIService上报日志
        // 暂时模拟成功
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (completion) {
                completion(YES, nil);
            }
        });
    });
}

@end
