//
//  Logger.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 日志级别
typedef NS_ENUM(NSInteger, LogLevel) {
    LogLevelVerbose = 0,
    LogLevelDebug = 1,
    LogLevelInfo = 2,
    LogLevelWarning = 3,
    LogLevelError = 4,
    LogLevelNone = 5
};

@interface Logger : NSObject

// 单例访问
+ (instancetype)sharedInstance;
+ (instancetype)new NS_UNAVAILABLE;
- (instancetype)init NS_UNAVAILABLE;

// 日志级别设置
@property (nonatomic, assign) LogLevel logLevel;

// 是否启用文件日志
@property (nonatomic, assign) BOOL fileLoggingEnabled;

// 日志文件路径
@property (nonatomic, readonly) NSString *logFilePath;

// 日志方法
+ (void)verbose:(NSString *)format, ... NS_FORMAT_FUNCTION(1,2);
+ (void)debug:(NSString *)format, ... NS_FORMAT_FUNCTION(1,2);
+ (void)info:(NSString *)format, ... NS_FORMAT_FUNCTION(1,2);
+ (void)warning:(NSString *)format, ... NS_FORMAT_FUNCTION(1,2);
+ (void)error:(NSString *)format, ... NS_FORMAT_FUNCTION(1,2);

// 实例方法
- (void)logWithLevel:(LogLevel)level format:(NSString *)format arguments:(va_list)arguments;
- (void)logWithLevel:(LogLevel)level message:(NSString *)message;

// 日志文件管理
- (void)clearLogFile;
- (nullable NSString *)getLogFileContent;
- (NSArray<NSString *> *)getRecentLogs:(NSUInteger)count;

// 日志上报
- (void)uploadLogsWithCompletion:(void(^)(BOOL success, NSError * _Nullable error))completion;

@end

// 便捷宏定义
#define LogV(format, ...) [Logger verbose:format, ##__VA_ARGS__]
#define LogD(format, ...) [Logger debug:format, ##__VA_ARGS__]
#define LogI(format, ...) [Logger info:format, ##__VA_ARGS__]
#define LogW(format, ...) [Logger warning:format, ##__VA_ARGS__]
#define LogE(format, ...) [Logger error:format, ##__VA_ARGS__]

NS_ASSUME_NONNULL_END
