//
//  SDKConfig.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SDKConfig : NSObject

// 基础配置
@property (nonatomic, strong) NSString *appId;
@property (nonatomic, strong) NSString *appSecret;
@property (nonatomic, strong) NSString *baseURL;
@property (nonatomic, assign) BOOL debugMode;

// 第三方SDK配置
@property (nonatomic, strong, nullable) NSString *adjustAppToken;
@property (nonatomic, strong, nullable) NSString *facebookAppId;

// 网络配置
@property (nonatomic, assign) NSTimeInterval requestTimeout;
@property (nonatomic, assign) NSInteger maxRetryCount;

// 缓存配置
@property (nonatomic, assign) NSTimeInterval cacheExpiration;
@property (nonatomic, assign) NSUInteger memoryCacheCapacity;

// 创建默认配置
+ (instancetype)defaultConfig;

// 配置验证
- (BOOL)isValid;
- (NSArray<NSString *> *)validateConfiguration;

@end

NS_ASSUME_NONNULL_END
