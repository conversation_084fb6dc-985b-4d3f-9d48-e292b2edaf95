//
//  SDKConfig.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "SDKConfig.h"
#import "Constants.h"

@implementation SDKConfig

#pragma mark - 初始化

- (instancetype)init {
    if (self = [super init]) {
        // 设置默认值
        self.baseURL = [Constants baseURL];
        self.debugMode = NO;
        self.requestTimeout = 30.0;
        self.maxRetryCount = 3;
        self.cacheExpiration = 3600.0; // 1小时
        self.memoryCacheCapacity = 100;
        self.adjustAppToken = [Constants adjustAppToken];
    }
    return self;
}

#pragma mark - 工厂方法

+ (instancetype)defaultConfig {
    return [[self alloc] init];
}

#pragma mark - 配置验证

- (BOOL)isValid {
    NSArray<NSString *> *errors = [self validateConfiguration];
    return errors.count == 0;
}

- (NSArray<NSString *> *)validateConfiguration {
    NSMutableArray<NSString *> *errors = [NSMutableArray array];
    
    // 验证必需的配置项
    if (!self.appId || self.appId.length == 0) {
        [errors addObject:@"appId不能为空"];
    }
    
    if (!self.appSecret || self.appSecret.length == 0) {
        [errors addObject:@"appSecret不能为空"];
    }
    
    if (!self.baseURL || self.baseURL.length == 0) {
        [errors addObject:@"baseURL不能为空"];
    } else {
        // 验证URL格式
        NSURL *url = [NSURL URLWithString:self.baseURL];
        if (!url || (!url.scheme || ![url.scheme hasPrefix:@"http"])) {
            [errors addObject:@"baseURL格式无效"];
        }
    }
    
    // 验证数值范围
    if (self.requestTimeout <= 0) {
        [errors addObject:@"requestTimeout必须大于0"];
    }
    
    if (self.maxRetryCount < 0) {
        [errors addObject:@"maxRetryCount不能小于0"];
    }
    
    if (self.cacheExpiration < 0) {
        [errors addObject:@"cacheExpiration不能小于0"];
    }
    
    if (self.memoryCacheCapacity == 0) {
        [errors addObject:@"memoryCacheCapacity不能为0"];
    }
    
    return [errors copy];
}

#pragma mark - 描述

- (NSString *)description {
    return [NSString stringWithFormat:@"SDKConfig: {\n"
            @"  appId: %@\n"
            @"  baseURL: %@\n"
            @"  debugMode: %@\n"
            @"  requestTimeout: %.1f\n"
            @"  maxRetryCount: %ld\n"
            @"  cacheExpiration: %.1f\n"
            @"  memoryCacheCapacity: %lu\n"
            @"}",
            self.appId ?: @"(null)",
            self.baseURL ?: @"(null)",
            self.debugMode ? @"YES" : @"NO",
            self.requestTimeout,
            (long)self.maxRetryCount,
            self.cacheExpiration,
            (unsigned long)self.memoryCacheCapacity];
}

@end
