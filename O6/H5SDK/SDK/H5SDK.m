//
//  H5SDK.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "H5SDK.h"
#import "InitializationManager.h"
#import "PaymentManager.h"
#import "GlobalDataManager.h"
#import "DeviceInfoManager.h"
#import "LoginViewController.h"
#import "WebViewController.h"
#import "ConfigManager.h"
#import "Constants.h"

@interface H5SDK ()

@property (class, nonatomic, assign) BOOL debugMode;

@end

@implementation H5SDK

static BOOL _debugMode = NO;

#pragma mark - SDK版本

+ (NSString *)version {
    return [Constants sdkVersion];
}

#pragma mark - 调试模式

+ (void)setDebugMode:(BOOL)enabled {
    _debugMode = enabled;
    if (enabled) {
        NSLog(@"H5SDK调试模式已开启");
    }
}

+ (BOOL)debugMode {
    return _debugMode;
}

#pragma mark - 初始化SDK

+ (void)initializeWithCompletion:(H5SDKInitializationHandler)completion {
    if ([self debugMode]) {
        NSLog(@"H5SDK开始初始化，版本: %@", [self version]);
    }
    
    InitializationManager *initManager = [InitializationManager sharedInstance];
    
    if (initManager.isInitialized) {
        if ([self debugMode]) {
            NSLog(@"H5SDK已经初始化完成");
        }
        if (completion) {
            completion(YES, nil);
        }
        return;
    }
    
    [initManager startInitializationWithCompletion:^(BOOL success, NSError *error) {
        if ([self debugMode]) {
            if (success) {
                NSLog(@"H5SDK初始化成功");
            } else {
                NSLog(@"H5SDK初始化失败: %@", error.localizedDescription);
            }
        }
        
        if (completion) {
            completion(success, error);
        }
    }];
}

#pragma mark - 显示页面

+ (void)showLoginPageFromViewController:(UIViewController *)viewController 
                             completion:(H5SDKInitializationHandler)completion {
    
    if (!viewController) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"H5SDKError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"Parent view controller cannot be empty"}];
            completion(NO, error);
        }
        return;
    }
    
    if ([self debugMode]) {
        NSLog(@"H5SDK显示登录页面");
    }
    
    LoginViewController *loginVC = [[LoginViewController alloc] initWithCompletion:completion];
    loginVC.modalPresentationStyle = UIModalPresentationFullScreen;
    
    [viewController presentViewController:loginVC animated:YES completion:nil];
}

+ (void)showMainPageFromViewController:(UIViewController *)viewController {
    if (!viewController) {
        NSLog(@"父视图控制器不能为空");
        return;
    }
    
    if (![self isInitialized]) {
        NSLog(@"SDK未初始化，无法显示主页面");
        return;
    }
    
    ConfigManager *configManager = [ConfigManager sharedInstance];
    NSString *h5URL = [configManager getH5FullPath];
    
    if (!h5URL || h5URL.length == 0) {
        NSLog(@"无法获取H5页面地址");
        return;
    }
    
    if ([self debugMode]) {
        NSLog(@"H5SDK显示主页面: %@", h5URL);
    }
    
    WebViewController *webVC = [[WebViewController alloc] initWithURL:h5URL];
    webVC.modalPresentationStyle = UIModalPresentationFullScreen;
    
    [viewController presentViewController:webVC animated:YES completion:nil];
}

#pragma mark - 支付功能

+ (void)purchaseProduct:(NSString *)productCode 
                 source:(nullable NSString *)source 
                  entry:(nullable NSString *)entry 
             completion:(H5SDKPaymentHandler)completion {
    
    if (!productCode || productCode.length == 0) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"H5SDKError"
                                                 code:-1002
                                             userInfo:@{NSLocalizedDescriptionKey: @"Product code cannot be empty"}];
            completion(NO, error);
        }
        return;
    }
    
    if (![self isInitialized]) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"H5SDKError" 
                                                 code:-1003 
                                             userInfo:@{NSLocalizedDescriptionKey: @"SDK未初始化"}];
            completion(NO, error);
        }
        return;
    }
    
    if (![self isLoggedIn]) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"H5SDKError"
                                                 code:-1004
                                             userInfo:@{NSLocalizedDescriptionKey: @"User not logged in"}];
            completion(NO, error);
        }
        return;
    }
    
    if ([self debugMode]) {
        NSLog(@"H5SDK发起支付: %@, source: %@, entry: %@", productCode, source, entry);
    }
    
    PaymentManager *paymentManager = [PaymentManager sharedInstance];
    [paymentManager purchaseProduct:productCode 
                             source:source 
                              entry:entry 
                         completion:completion];
}

+ (void)restorePurchasesWithCompletion:(H5SDKPaymentHandler)completion {
    if (![self isInitialized]) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"H5SDKError" 
                                                 code:-1003 
                                             userInfo:@{NSLocalizedDescriptionKey: @"SDK未初始化"}];
            completion(NO, error);
        }
        return;
    }
    
    if ([self debugMode]) {
        NSLog(@"H5SDK恢复购买");
    }
    
    PaymentManager *paymentManager = [PaymentManager sharedInstance];
    [paymentManager restorePurchasesWithCompletion:completion];
}

#pragma mark - SDK状态查询

+ (BOOL)isInitialized {
    InitializationManager *initManager = [InitializationManager sharedInstance];
    return initManager.isInitialized;
}

+ (BOOL)isLoggedIn {
    GlobalDataManager *globalDataManager = [GlobalDataManager sharedInstance];
    return [globalDataManager isLoggedIn];
}

#pragma mark - 获取信息

+ (nullable NSDictionary *)getUserInfo {
    if (![self isLoggedIn]) {
        return nil;
    }
    
    GlobalDataManager *globalDataManager = [GlobalDataManager sharedInstance];
    return globalDataManager.userInfo;
}

+ (NSDictionary *)getDeviceInfo {
    DeviceInfoManager *deviceInfoManager = [DeviceInfoManager sharedInstance];
    
    return @{
        @"platform": @"iOS",
        @"appVersion": deviceInfoManager.appVersion,
        @"deviceId": deviceInfoManager.deviceId,
        @"deviceModel": deviceInfoManager.deviceModel,
        @"systemVersion": deviceInfoManager.systemVersion,
        @"languageCode": deviceInfoManager.languageCode,
        @"countryCode": deviceInfoManager.countryCode,
        @"timeZone": deviceInfoManager.timeZone,
        @"bundleIdentifier": deviceInfoManager.bundleIdentifier,
        @"isVPNEnabled": @(deviceInfoManager.isVPNEnabled),
        @"isProxyEnabled": @(deviceInfoManager.isProxyEnabled),
        @"isJailbroken": @(deviceInfoManager.isJailbroken),
        @"isSimulator": @(deviceInfoManager.isSimulator)
    };
}

#pragma mark - 清理数据

+ (void)clearAllData {
    if ([self debugMode]) {
        NSLog(@"H5SDK清理所有数据");
    }
    
    GlobalDataManager *globalDataManager = [GlobalDataManager sharedInstance];
    [globalDataManager clearAllData];
    
    // 重置初始化状态
    InitializationManager *initManager = [InitializationManager sharedInstance];
    [initManager resetInitializationState];
    
    // 重置支付状态
    PaymentManager *paymentManager = [PaymentManager sharedInstance];
    [paymentManager resetPaymentState];
}

@end
