//
//  LoginViewController.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "LoginViewController.h"
#import "InitializationManager.h"
#import "WebViewController.h"
#import "ConfigManager.h"
#import "Constants.h"

@interface LoginViewController ()

@property (nonatomic, strong) UIButton *fastLoginButton;
@property (nonatomic, strong) UIButton *agreementButton;
@property (nonatomic, strong) UILabel *agreementLabel;
@property (nonatomic, strong) UIActivityIndicatorView *loadingIndicator;
@property (nonatomic, strong) UIImageView *backgroundImageView;
@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, assign) BOOL isAgreementAccepted;
@property (nonatomic, assign) BOOL isInitializing;

@property (nonatomic, strong) InitializationManager *initializationManager;
@property (nonatomic, strong) ConfigManager *configManager;

@end

@implementation LoginViewController

#pragma mark - 初始化方法

- (instancetype)initWithCompletion:(LoginCompletionHandler)completion {
    if (self = [super init]) {
        self.loginCompletionHandler = completion;
        self.isAgreementAccepted = NO;
        self.isInitializing = NO;
        self.initializationManager = [InitializationManager sharedInstance];
        self.configManager = [ConfigManager sharedInstance];
    }
    return self;
}

#pragma mark - 生命周期

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupConstraints];
    [self startInitialization];
}

#pragma mark - UI设置

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];

    // 背景图片
    self.backgroundImageView = [[UIImageView alloc] init];
    self.backgroundImageView.image = [UIImage imageNamed:[Constants launchImageName]];
    self.backgroundImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.backgroundImageView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.backgroundImageView];

    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"Welcome";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:24];
    self.titleLabel.textColor = [UIColor blackColor];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.titleLabel];

    // 快速登录按钮
    self.fastLoginButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.fastLoginButton setTitle:@"Fast Login" forState:UIControlStateNormal];
    [self.fastLoginButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.fastLoginButton.backgroundColor = [UIColor systemBlueColor];
    self.fastLoginButton.layer.cornerRadius = 25;
    self.fastLoginButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    self.fastLoginButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.fastLoginButton addTarget:self action:@selector(fastLoginButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.fastLoginButton];

    // 协议同意按钮
    self.agreementButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.agreementButton setImage:[UIImage systemImageNamed:@"square"] forState:UIControlStateNormal];
    [self.agreementButton setImage:[UIImage systemImageNamed:@"checkmark.square.fill"] forState:UIControlStateSelected];
    self.agreementButton.tintColor = [UIColor systemBlueColor];
    self.agreementButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.agreementButton addTarget:self action:@selector(agreementButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.agreementButton];

    // 协议文本
    self.agreementLabel = [[UILabel alloc] init];
    self.agreementLabel.text = @"I have read and agree to the User Agreement and Privacy Policy";
    self.agreementLabel.font = [UIFont systemFontOfSize:14];
    self.agreementLabel.textColor = [UIColor grayColor];
    self.agreementLabel.numberOfLines = 0;
    self.agreementLabel.translatesAutoresizingMaskIntoConstraints = NO;
    self.agreementLabel.userInteractionEnabled = YES;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(agreementLabelTapped:)];
    [self.agreementLabel addGestureRecognizer:tapGesture];
    [self.view addSubview:self.agreementLabel];

    // 加载指示器
    self.loadingIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
    self.loadingIndicator.color = [UIColor systemBlueColor];
    self.loadingIndicator.translatesAutoresizingMaskIntoConstraints = NO;
    self.loadingIndicator.hidden = YES;
    [self.view addSubview:self.loadingIndicator];

    // 初始状态
    [self updateUIState];
}

- (void)setupConstraints {
    [NSLayoutConstraint activateConstraints:@[
        // 背景图片
        [self.backgroundImageView.topAnchor constraintEqualToAnchor:self.view.topAnchor],
        [self.backgroundImageView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.backgroundImageView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.backgroundImageView.bottomAnchor constraintEqualToAnchor:self.view.bottomAnchor],

        // 标题
        [self.titleLabel.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.titleLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:100],

        // 快速登录按钮
        [self.fastLoginButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.fastLoginButton.bottomAnchor constraintEqualToAnchor:self.agreementButton.topAnchor constant:-30],
        [self.fastLoginButton.widthAnchor constraintEqualToConstant:200],
        [self.fastLoginButton.heightAnchor constraintEqualToConstant:50],

        // 协议按钮
        [self.agreementButton.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:30],
        [self.agreementButton.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-50],
        [self.agreementButton.widthAnchor constraintEqualToConstant:24],
        [self.agreementButton.heightAnchor constraintEqualToConstant:24],

        // 协议文本
        [self.agreementLabel.leadingAnchor constraintEqualToAnchor:self.agreementButton.trailingAnchor constant:8],
        [self.agreementLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-30],
        [self.agreementLabel.centerYAnchor constraintEqualToAnchor:self.agreementButton.centerYAnchor],

        // 加载指示器
        [self.loadingIndicator.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],
        [self.loadingIndicator.centerYAnchor constraintEqualToAnchor:self.fastLoginButton.centerYAnchor]
    ]];
}

#pragma mark - 按钮事件

- (void)fastLoginButtonTapped {
    if (!self.isAgreementAccepted) {
        [self showAlert:@"Please agree to the User Agreement and Privacy Policy first"];
        return;
    }

    if (self.isInitializing) {
        // 正在初始化中，显示加载状态
        [self updateUIState];
        return;
    }

    if (self.initializationManager.isInitialized) {
        [self handleInitializationCompleted];
    } else {
        [self performLogin];
    }
}

- (void)agreementButtonTapped {
    self.isAgreementAccepted = !self.isAgreementAccepted;
    self.agreementButton.selected = self.isAgreementAccepted;
    [self updateUIState];
}

- (void)agreementLabelTapped:(UITapGestureRecognizer *)gesture {
    // 检测点击的是哪个协议
    CGPoint location = [gesture locationInView:self.agreementLabel];
    NSString *text = self.agreementLabel.text;

    if ([text containsString:@"User Agreement"]) {
        [self openAgreementPage:[Constants serviceURL]];
    } else if ([text containsString:@"Privacy Policy"]) {
        [self openAgreementPage:[Constants policyURL]];
    }
}

#pragma mark - 登录处理

- (void)performLogin {
    if (self.initializationManager.isInitialized) {
        [self handleInitializationCompleted];
    } else {
        [self startInitialization];
    }
}

- (void)startInitialization {
    self.isInitializing = YES;
    [self updateUIState];

    __weak typeof(self) weakSelf = self;
    [self.initializationManager startInitializationWithCompletion:^(BOOL success, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakSelf.isInitializing = NO;
            if (success) {
                [weakSelf handleInitializationCompleted];
            } else {
                [weakSelf handleInitializationFailed:error];
            }
        });
    }];
}

- (void)handleInitializationCompleted {
    NSLog(@"初始化完成，准备进入主界面");

    // 获取H5页面URL
    NSString *h5URL = [self.configManager getH5FullPath];
    if (!h5URL || h5URL.length == 0) {
        [self showAlert:@"Configuration error, unable to get H5 page address"];
        return;
    }

    // 创建WebViewController
    WebViewController *webVC = [[WebViewController alloc] initWithURL:h5URL];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:webVC];
    navController.modalPresentationStyle = UIModalPresentationFullScreen;

    [self presentViewController:navController animated:YES completion:^{
        // 调用登录完成回调
        if (self.loginCompletionHandler) {
            self.loginCompletionHandler(YES, nil);
        }
    }];
}

- (void)handleInitializationFailed:(NSError *)error {
    NSLog(@"初始化失败: %@", error.localizedDescription);

    NSString *message = error.localizedDescription ?: @"Initialization failed, please try again";
    [self showAlert:message];

    // 调用登录完成回调
    if (self.loginCompletionHandler) {
        self.loginCompletionHandler(NO, error);
    }
}

#pragma mark - UI状态更新

- (void)updateUIState {
    BOOL canLogin = self.isAgreementAccepted && !self.isInitializing;

    self.fastLoginButton.enabled = canLogin;
    self.fastLoginButton.alpha = canLogin ? 1.0 : 0.6;

    if (self.isInitializing) {
        self.fastLoginButton.hidden = YES;
        self.loadingIndicator.hidden = NO;
        [self.loadingIndicator startAnimating];
    } else {
        self.fastLoginButton.hidden = NO;
        self.loadingIndicator.hidden = YES;
        [self.loadingIndicator stopAnimating];
    }
}

#pragma mark - 协议页面

- (void)openAgreementPage:(NSString *)url {
    if (!url || url.length == 0) {
        [self showAlert:@"Invalid agreement page address"];
        return;
    }

    WebViewController *webVC = [[WebViewController alloc] initWithURLForProtocolContent:url];
    [self.navigationController pushViewController:webVC animated:YES];
}

#pragma mark - 工具方法

- (void)showAlert:(NSString *)message {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Notice"
                                                                   message:message
                                                            preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK"
                                                       style:UIAlertActionStyleDefault
                                                     handler:nil];
    [alert addAction:okAction];
    [self presentViewController:alert animated:YES completion:nil];
}

@end
