//
//  LoginViewController.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^LoginCompletionHandler)(BOOL success, NSError * _Nullable error);

@interface LoginViewController : UIViewController

// 初始化方法
- (instancetype)initWithCompletion:(LoginCompletionHandler)completion;

// 登录完成回调
@property (nonatomic, copy, nullable) LoginCompletionHandler loginCompletionHandler;

// UI元素
@property (nonatomic, strong, readonly) UIButton *fastLoginButton;
@property (nonatomic, strong, readonly) UIButton *agreementButton;
@property (nonatomic, strong, readonly) UILabel *agreementLabel;
@property (nonatomic, strong, readonly) UIActivityIndicatorView *loadingIndicator;

// 状态管理
@property (nonatomic, assign, readonly) BOOL isAgreementAccepted;
@property (nonatomic, assign, readonly) BOOL isInitializing;

// 手动触发登录
- (void)performLogin;

@end

NS_ASSUME_NONNULL_END
