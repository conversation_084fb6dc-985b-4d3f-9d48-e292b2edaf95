## 1. 增加登录和审核判断逻辑：

- 初始化的时候，判断是否已经登录，如果已经登录，则不调用登录接口，从本地存储里面恢复authData数据。
- 获取strategyData后，获取字段“isReviewPkg”的值，如果为 true，则延迟 2 秒再获取一次strategyData，再次判断“isReviewPkg”的值，如果仍然为 true，则抛出错误。

## 2. 轮询工具

- 调用getStatus接口，实现以下逻辑，功能保持简单高效
  - 提供一个函数，调用函数后，开始轮询接口，最大轮询时间120s。
  - 每次获取到字段sh的值为true，不返回，继续轮询。   
  - 只要获取到字段sh的值为false，则返回false，停止轮询。
  - 轮询开始前3次间隔1秒；后续轮询加大间隔为3s。
  - 超过最大时限后不再轮询，放弃。 

要求编写一个轮询函数，调用getStatus接口，实现特定的轮询逻辑：

功能需求分析：

最大轮询时间：120秒
轮询条件：sh为true时继续，sh为false时停止并返回false
轮询间隔：前3次间隔1秒，后续间隔3秒
超时处理：超过120秒后停止轮询


## 3. 加密工具类，保持加密工具类功能简单高效

- 使用CryptoSwift开源库，添加以下加密方法：
   - AES-ECB encryption using CryptoSwift，use pkcs7
   - AES-ECB decryption using CryptoSwift use pkcs7
   - Base64 encode a string
   - Base64 decode a string
- 加解密key需要做以下处理：
   - key to 32 characters (AES-256 requirement)
   - 超过32位，截取前32位
   - 不够32位，在后面补0

## 4. 网络请求添加加密拓展
- 将网络请求头headers的参数加入parameters中，使用字段“http_headers”
- 加密POST请求的parameters，先将parameters转为json字符串，然后加密为加密字符串，转为data，request的httpBody改为使用加密后的data。
- 解密接口返回的data数据，解密时需要对加密文本做以下处理：
   - .replacingOccurrences(of: "\r\n", with: ""
   - .trimmingCharacters(in: .whitespacesAndNewlines
- 不需要添加加解密开关属性。
- 加解密key说明：
   - 对于getAppConfig接口，使用baseURL的host作为key。
   - 对于其他接口，从appConfigData中使用getAppConfigString方法获取字段名为encrypt_key的值作为key。

## 5. AdjustManager和FacebookManager调整，保持功能简单高效

   - 合并AdjustManager和FacebookManager成一个类。
   - 精简Facebook的统计，只保留下单事件，购买事件记录。
   - 调整Adjust的统计，只实现购买事件，下单事件，登录事件，注册事件记录。

## 6. 以下常量需要作为配置，添加到SDK的初始化配置中，帮我完成相关实现，保持功能简单高效。
   
```swift
static let bundleIdPrefix: String = "com.gewingeath."

  /// Keychain service identifier
 static let keychainService = bundleIdPrefix + "keychain"

 static let baseURL = "https://test-app.lurve.cc"
 static let logURL = "https://test-log.lurve.cc"
 static let imURL = "wss://test-im.lurve.cc"

 // Service URLs
 static let serviceURL = "https://app-h5.lurve.cc/UserAgreement.html"
 static let policyURL = "https://app-h5.lurve.cc/PrivacyPolicy.html"

 static let appleId = "6746118609"
 static let sdkVersion = "2.1.2"

 /// NetWork key
 static let key1 = "Intelligence"
 static let key2 = "Artificial"
 static let key3 = "Marathon"
 static let key4 = "Chatbot"

 /// API paths
 static let appConfig = "/config/getAppConfigPostV2"
 static let oauth = "/security/oauth"
 static let cassia = "/config/getStrategyPostV2"
 static let createOrder = "/coin/recharge/create"
 static let consumeIAP = "/coin/recharge/payment/ipa"
 static let recordReqs = "/hit/ascribeRecordReqs"
 static let riskReport = "/risk/info/upload"
 static let shStatus = "/config/sh"
 static let logRecord = "/log/liveChatPostV2"

 /// Adjust configuration
 static let adjustAppToken = "lgvu8ln3o45c"
 static let adjustOrder = "4dnrmi"
 static let adjustPurchase = "l0ramk"
 static let adjustLogin = ""
 static let adjustRegister = ""

 static let image_launch_name = "launch"
 ```

## 7. 完善设备信息获取，增加以下信息。

- 是否有中文键盘
- 是否支持密码解锁
- 是否支持面容解锁
- 是否支持TouchID解锁
- 是否开启VPN
- 是否开启代理
- 是否越狱
- 给定一个字符串数组 schemes（包含多个应用的 URL scheme，例如 ["weixin", "alipay", "myapp"]），编写一个函数，判断当前 iOS 设备中已安装了多少个能响应这些 scheme 的应用。

## 8. 编写一个函数，返回一个Bool值，逻辑如下：

- 判断isDominateMonitoringActive是否开启，如果开启，对比已安装应数量和dominateCountKey的数量，如果小于dominateCountKey的数量，则返回true。
- 已安装应用数量使用LSApplicationQueriesSchemes来计算
- 接着判断inputCheckKey是否开启，如果开启，按照以下逻辑判断：
   - 如果设备包含中文键盘，则返回true
   - 如果设备不支持密码解锁，并且不支持面容解锁，并且不支持指纹解锁，则返回true

## 9. 风控数据定义与加密
 
 - 定义以下风控数据：
   ```swift
   "platform": "iOS",
   "pkg": bundleId,
   "ver": appVersion,
   "platform_ver": systemVersion,
   "model": deviceModel,
   "user_id": userId ?? "",
   "device_id": deviceId,
   "system_language": languageCode,
   "time_zone": timeZone,
   "is_enable_vpn": isVPNEnabled() ? 1 : 0,
   "is_enable_proxy": isProxyEnabled() ? 1 : 0,
   "is_jailbreaking": isJailbroken() ? 1 : 0,
   "is_emulator": isSimulator() ? 1 : 0
   ```
 - 风控数据加密函数：
   - 风控字典转换为jsonData，jsonData转换为jsonString
   - 使用加密工具类AES-ECB加密jsonString。
   - 加密key：从appConfigData中获取riskControlInfoConfig，再从riskControlInfoConfig中获取字段为k_factor的值。

## 10. 完成handleAttribution方法，上报以下信息
   ```swift
   "utmSource": attribution.network?.urlEncoded ?? "",
   "adgroupId": attribution.adgroup?.urlEncoded ?? "",
   "adsetId": attribution.creative?.urlEncoded ?? "",
   "campaignId": attribution.campaign?.urlEncoded ?? "",
   "attributionSdk": "AJ",
   "attributionSdkVer": Adjust.sdkVersion() ?? "",
   "device-id": deviceInfo.deviceId,
   "ver": deviceInfo.appVersion,
   "pkg": deviceInfo.bundleId,
   "userId": userId ?? "",
   ```

## 11. 完善全功能网页事件监听和事件调用

- 请基于以下已定义的结构常量 WebView，为我实现完整的网页事件监听与事件调用支持逻辑。
- 事件名改为使用数组的下标读取，方便我修改事件名。
-  messageHandlers 是包含在“event”事件中的。
- 参数使用数组下标读取。
- keyboardWillShow 和 keyboardWillHide 向网页传递键盘高度(只传递键盘高度（算上屏幕分辨率）)
- 键盘隐藏和显示时，同时修改webView底部的约束，与键盘顶部对齐。

   ```swift
   /// WebView related constants
    struct WebView {
        /// Message handlers list - final version
        /// 注册响应网页的消息处理事件
        static let messageHandlersFinal = [
            "event",
            "newTppClose",
            "openVipService",
            "recharge"
        ]

        /// Message handlers list
        /// 消息事件名为event时，message.body 解析为字典，使用eventKeys[0]作为键，从字典中获取到具体的事件(可能下列定义中的任意一个)。
        /// 根据不同事件，处理不同逻辑。
        /// 事件中的参数，从eventKeys[1]到eventKeys[9]中取值。
        /// 例如：打开浏览器事件，获取地址和类型。
        /// guard let urlString = eventData[Constants.WebView.eventKeys[1]] as? String,
        ///    let type = eventData[Constants.ContentView.eventKeys[2]] as? Int else { return }
        static let messageHandlers = [
            "logOut",           // Logout
            "OpenExternal",     // Open external
            "PayLog",           // Payment tracking report
            "Pay",              // Payment
            "OpenSettings",     // Open settings
            "WakelockSet",      // Prevent screen sleep
            "SetToken",         // Update token
            "LoadingSuccess",   // Loading success
            "my_load_recharge"  // Load recharge status
        ]

        /// Message event keys
        static let eventKeys = [
            "action",
            "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9"
        ]

        /// Application events list
        /// 原生应用调用网页的事件名定义
        /// 使用方式：
        /// let script = "window.onAppEvent('\(name)','\(value)');"
        ///    webView.evaluateJavaScript(script) { _, error in
        ///        if let error = error {
        ///          print("error: \(error.localizedDescription)")
        ///        }
        ///     }
        static let appEvents = [
            "payFailed",          // Payment failed
            "paySuccess",         // Payment success
            "AppLifecycleState",  // App lifecycle state
            "updateHeader",       // Update header
            "keyboardWillShow",   // Keyboard will show
            "keyboardWillHide",   // Keyboard will hide
            "my_load_recharge"    // Notify H5 to get recharge status
        ]
    }
    ```
## 12. 按照以下方式初始化网页参数(nativeInitData)
```swift
        var data: [String: Any] = [:]
        let scriptProperties = Constants.ContentView.scriptProperties

        // App configuration
        if let appConfigData = session.appConfigData {
            data[scriptProperties[0]] = appConfigData
        }

        // Strategy configuration
        if let cassiaConfigData = session.cassiaConfigData {
            data[scriptProperties[1]] = cassiaConfigData
        }

        // Login user information
        if let authData = session.oauthData {
            data[scriptProperties[2]] = authData
        }

        // Device and API information
        data[scriptProperties[3]] = session.getDeviceId()
        data[scriptProperties[4]] = Constants.baseURL
        data[scriptProperties[5]] = Constants.logURL
        data[scriptProperties[6]] = Constants.imURL
        data[scriptProperties[7]] = Constants.policyURL
        data[scriptProperties[8]] = Constants.serviceURL
        data[scriptProperties[9]] = Constants.appleId
        data[scriptProperties[10]] = Bundle.main.bundleIdentifier ?? ""

        // Security keys
        data[scriptProperties[11]] = [
            Constants.key1,
            Constants.key2,
            Constants.key3,
            Constants.key4
        ]

        // App version and risk information
        data[scriptProperties[12]] = Constants.sdkVersion
        data[scriptProperties[13]] = risk.getRiskInfo()

        // Add message handlers and constants
        data[Constants.ContentView.keys[0]] = Constants.ContentView.messageHandlers
        data[Constants.ContentView.keys[1]] = Constants.ContentView.scriptProperties
        data[Constants.ContentView.keys[2]] = Constants.ContentView.appEvents
        data[Constants.ContentView.keys[3]] = Constants.ContentView.eventKeys
        data["k"] = Constants.ContentView.keys

        guard let jsonData = try? JSONSerialization.data(withJSONObject: scriptData),
              let jsonString = String(data: jsonData, encoding: .utf8),
              let base64EncodedData = jsonString.data(using: .utf8)?.base64EncodedString() else {
            return WKUserScript(source: "", injectionTime: .atDocumentStart, forMainFrameOnly: true)
        }

        let script = """
        window.nativeInitData = \(jsonString);
        window.isNativeApp = true;
        """

        return WKUserScript(source: script, injectionTime: .atDocumentStart, forMainFrameOnly: true)

```

将handleEvent方法中的switch语句改为处理internalHandlers数组中的11个事件类型：

"logOut" - 处理登出
"OpenExternal" - 打开外部链接
"PayLog" - 支付日志
"Pay" - 支付处理
"OpenSettings" - 打开设置
"WakelockSet" - 设置屏幕常亮
"SetToken" - 设置令牌
"LoadingSuccess" - 加载成功
"my_load_recharge" - 我的充值加载
"UpdatePolicy" - 更新策略
"ImageSelected" - 图片选择

## 13. 根据Appconfig接口返回的数据结构，添加获取配置信息的便捷方法
{"code":0,"success":true,"data":{"items":[{"name":"glt","data":"AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"rck","data":"qf3d5gbjq92th"},{"name":"rc_app_key","data":"lmxuhwagl7lid"},{"name":"rc_area_code","data":"SG"},{"name":"rc_navi_server","data":"nav.sg-light-edge.com"},{"name":"rc_stat_server","data":"stats.sg-light-edge.com"},{"name":"rtck","data":"e72270b7a0b4463989252f55e68ff731"},{"name":"zego_app_id","data":396347616},{"name":"simple-performance-network","data":"0.1"},{"name":"log_cdn_performance","data":"true"},{"name":"tpp_ua","data":"0"},{"name":"im_greeting","data":"0"},{"name":"anchor_mask","data":"0"},{"name":"user_behavior_submit_enable","data":"1"},{"name":"mg_broadcaster_call_enable_max_seconds","data":3600},{"name":"encrypt_key","data":"f919268214fa4ed7a0e9fcb2136d1ba0"},{"name":"is_encrypt","data":"true"},{"name":"is_open_invitation","data":"false"},{"name":"tpp_open_type","data":"1"},{"name":"app_ext_data","data":{"h5_subpath":"\/h5_web\/v6\/","flash_noti_enabled":"1","flash_time":"3600","banner_enabled":"1","flash_times":"[240,60,60,60]","flash_wait_content":"Wait for free tomorrow.\nOr recharge to match now!","app_dominate":"0","app_dominate_count":"5","banners":"[{\"cover_url\":\"2023-05-17\/h5banner\/youxi.png\",\"jump_url\":\"http:\/\/game.abv.cn\/00lobby\/?sign={sign}&ext=hall\",\"type\":1}]","loading_img_url":"https:\/\/test-h5.snoperp.com\/h5_web\/v5\/assets\/assets\/audio\/login_video.jpeg","explore_tab":"[{\"title\":\"Discover\",\"type\":\"discover\"}]","flash_noti_content":"You've got a free match. Video Chat with global girls now!","app_screenshot_enabled":"1","flash_noti_title":"Free Video Chat with Girls!","app_screenshot_num":"7","app_input_check_enabled":"0","app_sub_dominate":"0","safety":"EWpv1a4z6bP3WKwbRtpdmRGmPNk7RrfbpmGQn8x1kcSxBYvpWimQqCaoIH5zZ\/h5C9uE8rsRz\/JZ\/sjjBOvsB8Bn\/ijfkg1EYY+MSWygZb1r0Prr0Ho6VtoeP7FlSa\/kcQ2E+ZoNchTcLQfA4LMuPBsUw7\/HWuwc2DVXarbDkWM=","h5_fullpath":"http:\/\/test-h5.snoperp.com\/h5_web\/v6\/","flash_no_perrsion":"Allow notifications to get free match alerts. Go to Settings and turn them on now!"}},{"name":"translate_v2","data":"https:\/\/translation.googleapis.com\/language\/translate\/v2?key=AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"google_translation_key","data":"AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"microsoft_translation_key","data":""},{"name":"translate_type","data":2},{"name":"ios_app_id","data":"6746118609"},{"name":"is_enable_email_login","data":"false"},{"name":"anchor_netspeedcheck_enable","data":"1"},{"name":"hack_revenue_factor","data":0.29999999999999999},{"name":"attribution_sdk","data":"AF"},{"name":"anchor_netspeedcheck","data":{"url":"https:\/\/dldir1.qq.com\/weixin\/Windows\/WeChatSetup.exe","sizeLimit":30,"interval":172800000}},{"name":"time_log_upload_num","data":"1000"},{"name":"time_log_upload_backup","data":"0"},{"name":"anchor_beauty_min_frame","data":0},{"name":"anchor_beauty_max_cost","data":200},{"name":"anchor_beauty_min_time","data":20},{"name":"pgstts","data":1}],"rvsta":"2","ver":"6","riskControlInfoConfig":{"k_interval":5,"k_factor_num":"TJA160","k_factor":"c8ehl28i8nowbuai"}},"fail":false,"key":"common_success","msg":"成功"}


## 14. 特别处理Appconfig中的app_ext_data数据
   ### 14.1 解密safety字段，使用aesECB解密，解密key使用baseURL的host，解密结果预期为字典。
{
    "name": "app_ext_data",
    "data":
    {
        "safety": "EWpv1a4z6bP3WKwbRtpdmRGmPNk7RrfbpmGQn8x1kcSxBYvpWimQqCaoIH5zZ/h5C9uE8rsRz/JZ/sjjBOvsB8Bn/ijfkg1EYY+MSWygZb1r0Prr0Ho6VtoeP7FlSa/kcQ2E+ZoNchTcLQfA4LMuPBsUw7/HWuwc2DVXarbDkWM=",
    }
},

## 15. 第三方统计功能补充
1. 将三方SDK的初始化提前到第一位，初始化过程不等待
2. 登录成功后，主动上报一次归因数据（归因上报做是否登录的判断和AppconfigData不为空的判断），上报过程不等待。
3. 三方SDK初始化前，先请求Tracking权限（异步）。
4. Adjust初始化成功后，获取一次归因数据，如果有则上报。

## 16. 添加启动页面
   ### 16.1 页面结构：全屏启动图，底部一个加载indicator，距离底部安全区域50pt。
   ### 16.2 将window?.rootViewController设置为启动页面。
   ### 16.3 将SDK初始化的逻辑从AppDelegate和SceneDelegate迁移到启动页面中。
   ### 16.4 ContentHostController的h5URL从AppDataManager中的getSafetyData()中获取，字段为“h5_fullpath”。
   ### 16.5 初始化成功后，present ContentHostController。

## 17. 内购管理器优化
   ### 17.0 发起内购增加参数: source 和 entry，用于创建订单参数。
   ### 17.1 示例内购管理器的使用方式
   ### 17.2 预期内购正常流程：发起内购->查询商品（处理查询异常等情况）->创建订单->内购购买商品->购买成功->验证内购凭证
   ### 17.3 如果目前的使用方式不合理，优化内购管理器。


## 18. 添加 userDidTakeScreenshotNotification，didEnterBackgroundNotification， didBecomeActiveNotification 监听
   ### 18.1 didEnterBackgroundNotification 和 didBecomeActiveNotification 调用网页事件 AppLifecycleState， 参数 paused 和 resumed
   ### 18.2 截屏事件处理：判断Appconfig中是否开启app_screenshot_enabled截屏风控，如果开启则记录截屏次数，如果大于或者等于截屏次数并且用户不是充值用户（使用isRecharge字段判断），则本地记录用户进入冻结模式。

## 19. 登录页
- 我提供了设计图，请按照设计图开发页面。
- 初始化流程是进入登录页面就开始异步执行。
- Fast Login”按钮点击后需要先校验协议同意状态，点击后根据初始化结果判断跳转的页面。
- 如果点击登录按钮后，SDK正在初始化中，还未有结果，则显示loadingIndicator，隐藏登录按钮和协议栏，等待初始化结果做跳转。

## 20. 解密加密后的AppConfig数据
   20.1 给定一个数组 ["Jumping", "Extreme", "Equipment", "Harness"]，可以定义在Constants中。
   20.2 接口加密后，AppCoinfig的数据被加密，需要手动解密。
   20.3 取给定数组的第2位，第3位和第4位的值，作为键，从data中分别获取对应的值。
   20.4 将第2位和第3位的值使用base64解码后，合并成一个新的字符串
   20.5 将第4位的值使用base64解码成字符串，然后使用合并的字符串作为解密key，使用aesECBEncrypt解密解码的字符串。
   20.6 将解密结果转为字典，重新赋值给data字段。

- 定义数组: 在Constants中定义 ["Jumping", "Extreme", "Equipment", "Harness"]
- 特殊解密逻辑: AppConfig接口返回的data字段包含加密数据，需要额外解密
- 解密步骤:
  - 取数组第2、3、4位的值作为键（"Extreme", "Equipment", "Harness"）
  - 从data中获取对应的值
  - 将第2位和第3位的值base64解码后合并成解密key
  - 将第4位的值base64解码，然后用合并的key进行AES-ECB解密
  - 将解密结果转为字典，重新赋值给data字段

   ```
   ["key": common_success, "data": {
    Equipment = "YmY2OTYzOTJkNzJiNjQ3Mg==";
    Extreme = "ZjgwN2M1YzNjYjZkNDNlYQ==";
    Harness = 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;
    Jumping = "M01nUEhGejBwc1pDUkJXWg==";
    category = 3451;
    isWebApp = false;
}, "code": 0, "fail": 0, "success": 1, "msg": 成功]
```


## 21. 修改或实现原生通知网页
  21.1 需实现的通知，以下为事件名称：
   static let appEvents = [
      "payFailed",          // Payment failed
      "paySuccess",         // Payment success
      "AppLifecycleState",  // App lifecycle state
      "updateHeader",       // Update header
      "keyboardWillShow",   // Keyboard will show
      "keyboardWillHide",   // Keyboard will hide
      "my_load_recharge"    // Notify H5 to get recharge status
   ]
  22.2 调用的方式：
   let script = "\(window.onAppEvent)('\(eventName)','\(string)');"
   webView.evaluateJavaScript(script, completionHandler: completion)


## 22. 处理图片选择事件handleImageSelected：
1. 使用原生的picker，单次选择1张图
2. 图片压缩0.8，转换为base64字符串，使用notifyImageSelectedSuccess传递给网页。


## 23. loading 视图。
1. 非轻量模式下，webview 加载过程中，在webview上层添加一个loading覆盖视图（uiview）
2. loading视图需要有超时机制，超过120秒后，网页还未加载完成，则显示重试按钮和错误信息，点击重试，重新开始加载网页，重新开始计时。
3. 网页加载成功后，隐藏loading视图，停止定时器。
4. loading视图布局：全屏背景图，底部loadingindicator。


## 24. 优化初始化流程，实现缓存优先的策略：

AppConfig优化：如果有本地数据则跳过网络请求，然后异步请求接口刷新数据
AuthData和StrategyData优化：如果本地有数据则跳过网络请求