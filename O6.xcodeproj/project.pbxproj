// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		62155DDC3417A5C26379BFF8 /* Pods_O6.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E9388498C140EDC6917441A1 /* Pods_O6.framework */; };
		BECB70E42E2A38720084BFCC /* ConfigManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB709E2E2A38720084BFCC /* ConfigManager.m */; };
		BECB70E52E2A38720084BFCC /* GlobalDataManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70A02E2A38720084BFCC /* GlobalDataManager.m */; };
		BECB70E62E2A38720084BFCC /* InitializationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70A22E2A38720084BFCC /* InitializationManager.m */; };
		BECB70E72E2A38720084BFCC /* PaymentManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70A42E2A38720084BFCC /* PaymentManager.m */; };
		BECB70E82E2A38720084BFCC /* RiskControlManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70A62E2A38720084BFCC /* RiskControlManager.m */; };
		BECB70E92E2A38720084BFCC /* ThirdPartySDKManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70A82E2A38720084BFCC /* ThirdPartySDKManager.m */; };
		BECB70EA2E2A38720084BFCC /* Constants.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70AB2E2A38720084BFCC /* Constants.m */; };
		BECB70EB2E2A38720084BFCC /* CryptoUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70AD2E2A38720084BFCC /* CryptoUtils.m */; };
		BECB70EC2E2A38720084BFCC /* DeviceInfoManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70AF2E2A38720084BFCC /* DeviceInfoManager.m */; };
		BECB70ED2E2A38720084BFCC /* KeychainManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70B12E2A38720084BFCC /* KeychainManager.m */; };
		BECB70EE2E2A38720084BFCC /* UserDefaultsManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70B32E2A38720084BFCC /* UserDefaultsManager.m */; };
		BECB70EF2E2A38720084BFCC /* H5SDKExample.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70B52E2A38720084BFCC /* H5SDKExample.m */; };
		BECB70F12E2A38720084BFCC /* CacheService.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70BB2E2A38720084BFCC /* CacheService.m */; };
		BECB70F62E2A38720084BFCC /* H5SDK.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70C62E2A38720084BFCC /* H5SDK.m */; };
		BECB70F72E2A38720084BFCC /* SDKConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70C82E2A38720084BFCC /* SDKConfig.m */; };
		BECB70F82E2A38720084BFCC /* LoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70CC2E2A38720084BFCC /* LoginViewController.m */; };
		BECB70F92E2A38720084BFCC /* WebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70CE2E2A38720084BFCC /* WebViewController.m */; };
		BECB70FA2E2A38720084BFCC /* ErrorFactory.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70D12E2A38720084BFCC /* ErrorFactory.m */; };
		BECB70FB2E2A38720084BFCC /* Logger.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70D32E2A38720084BFCC /* Logger.m */; };
		BECB70FC2E2A38720084BFCC /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70D72E2A38720084BFCC /* AppDelegate.m */; };
		BECB70FD2E2A38720084BFCC /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70DC2E2A38720084BFCC /* main.m */; };
		BECB70FE2E2A38720084BFCC /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70E02E2A38720084BFCC /* SceneDelegate.m */; };
		BECB70FF2E2A38720084BFCC /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB70E22E2A38720084BFCC /* ViewController.m */; };
		BECB71002E2A38720084BFCC /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = BECB70D82E2A38720084BFCC /* Assets.xcassets */; };
		BECB71022E2A38720084BFCC /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BECB70DB2E2A38720084BFCC /* LaunchScreen.storyboard */; };
		BECB71032E2A38720084BFCC /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BECB70DE2E2A38720084BFCC /* Main.storyboard */; };
		BECB71092E2A38720084BFCC /* NetworkManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB71052E2A38720084BFCC /* NetworkManager.m */; };
		BECB710A2E2A38720084BFCC /* APIClient.m in Sources */ = {isa = PBXBuildFile; fileRef = BECB71072E2A38720084BFCC /* APIClient.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		3B6C6F8E312468D2D8A42481 /* Pods-O6.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-O6.release.xcconfig"; path = "Target Support Files/Pods-O6/Pods-O6.release.xcconfig"; sourceTree = "<group>"; };
		50E85EE69B3242514622F824 /* Pods-O6.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-O6.debug.xcconfig"; path = "Target Support Files/Pods-O6/Pods-O6.debug.xcconfig"; sourceTree = "<group>"; };
		B2BF46180EDA4518A167B4CB /* H5SDKExample.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = H5SDKExample.h; sourceTree = "<group>"; };
		BECB709D2E2A38720084BFCC /* ConfigManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ConfigManager.h; sourceTree = "<group>"; };
		BECB709E2E2A38720084BFCC /* ConfigManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ConfigManager.m; sourceTree = "<group>"; };
		BECB709F2E2A38720084BFCC /* GlobalDataManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GlobalDataManager.h; sourceTree = "<group>"; };
		BECB70A02E2A38720084BFCC /* GlobalDataManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GlobalDataManager.m; sourceTree = "<group>"; };
		BECB70A12E2A38720084BFCC /* InitializationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = InitializationManager.h; sourceTree = "<group>"; };
		BECB70A22E2A38720084BFCC /* InitializationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = InitializationManager.m; sourceTree = "<group>"; };
		BECB70A32E2A38720084BFCC /* PaymentManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PaymentManager.h; sourceTree = "<group>"; };
		BECB70A42E2A38720084BFCC /* PaymentManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PaymentManager.m; sourceTree = "<group>"; };
		BECB70A52E2A38720084BFCC /* RiskControlManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RiskControlManager.h; sourceTree = "<group>"; };
		BECB70A62E2A38720084BFCC /* RiskControlManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RiskControlManager.m; sourceTree = "<group>"; };
		BECB70A72E2A38720084BFCC /* ThirdPartySDKManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ThirdPartySDKManager.h; sourceTree = "<group>"; };
		BECB70A82E2A38720084BFCC /* ThirdPartySDKManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ThirdPartySDKManager.m; sourceTree = "<group>"; };
		BECB70AA2E2A38720084BFCC /* Constants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Constants.h; sourceTree = "<group>"; };
		BECB70AB2E2A38720084BFCC /* Constants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Constants.m; sourceTree = "<group>"; };
		BECB70AC2E2A38720084BFCC /* CryptoUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CryptoUtils.h; sourceTree = "<group>"; };
		BECB70AD2E2A38720084BFCC /* CryptoUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CryptoUtils.m; sourceTree = "<group>"; };
		BECB70AE2E2A38720084BFCC /* DeviceInfoManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeviceInfoManager.h; sourceTree = "<group>"; };
		BECB70AF2E2A38720084BFCC /* DeviceInfoManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeviceInfoManager.m; sourceTree = "<group>"; };
		BECB70B02E2A38720084BFCC /* KeychainManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = KeychainManager.h; sourceTree = "<group>"; };
		BECB70B12E2A38720084BFCC /* KeychainManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KeychainManager.m; sourceTree = "<group>"; };
		BECB70B22E2A38720084BFCC /* UserDefaultsManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UserDefaultsManager.h; sourceTree = "<group>"; };
		BECB70B32E2A38720084BFCC /* UserDefaultsManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UserDefaultsManager.m; sourceTree = "<group>"; };
		BECB70B52E2A38720084BFCC /* H5SDKExample.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = H5SDKExample.m; sourceTree = "<group>"; };
		BECB70BA2E2A38720084BFCC /* CacheService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CacheService.h; sourceTree = "<group>"; };
		BECB70BB2E2A38720084BFCC /* CacheService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CacheService.m; sourceTree = "<group>"; };
		BECB70C52E2A38720084BFCC /* H5SDK.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = H5SDK.h; sourceTree = "<group>"; };
		BECB70C62E2A38720084BFCC /* H5SDK.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = H5SDK.m; sourceTree = "<group>"; };
		BECB70C72E2A38720084BFCC /* SDKConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDKConfig.h; sourceTree = "<group>"; };
		BECB70C82E2A38720084BFCC /* SDKConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SDKConfig.m; sourceTree = "<group>"; };
		BECB70CB2E2A38720084BFCC /* LoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = LoginViewController.h; sourceTree = "<group>"; };
		BECB70CC2E2A38720084BFCC /* LoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = LoginViewController.m; sourceTree = "<group>"; };
		BECB70CD2E2A38720084BFCC /* WebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WebViewController.h; sourceTree = "<group>"; };
		BECB70CE2E2A38720084BFCC /* WebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WebViewController.m; sourceTree = "<group>"; };
		BECB70D02E2A38720084BFCC /* ErrorFactory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ErrorFactory.h; sourceTree = "<group>"; };
		BECB70D12E2A38720084BFCC /* ErrorFactory.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ErrorFactory.m; sourceTree = "<group>"; };
		BECB70D22E2A38720084BFCC /* Logger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Logger.h; sourceTree = "<group>"; };
		BECB70D32E2A38720084BFCC /* Logger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Logger.m; sourceTree = "<group>"; };
		BECB70D62E2A38720084BFCC /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		BECB70D72E2A38720084BFCC /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		BECB70D82E2A38720084BFCC /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		BECB70D92E2A38720084BFCC /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		BECB70DA2E2A38720084BFCC /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		BECB70DC2E2A38720084BFCC /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		BECB70DD2E2A38720084BFCC /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		BECB70DF2E2A38720084BFCC /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		BECB70E02E2A38720084BFCC /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		BECB70E12E2A38720084BFCC /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		BECB70E22E2A38720084BFCC /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		BECB71042E2A38720084BFCC /* NetworkManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NetworkManager.h; sourceTree = "<group>"; };
		BECB71052E2A38720084BFCC /* NetworkManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = NetworkManager.m; sourceTree = "<group>"; };
		BECB71062E2A38720084BFCC /* APIClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = APIClient.h; sourceTree = "<group>"; };
		BECB71072E2A38720084BFCC /* APIClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = APIClient.m; sourceTree = "<group>"; };
		BEE39CAA2E250630004F556A /* O6.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = O6.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E9388498C140EDC6917441A1 /* Pods_O6.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_O6.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BEE39CA72E250630004F556A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				62155DDC3417A5C26379BFF8 /* Pods_O6.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2CA9E7FE3C8077B3F6909239 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E9388498C140EDC6917441A1 /* Pods_O6.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BECB70A92E2A38720084BFCC /* Business */ = {
			isa = PBXGroup;
			children = (
				BECB709D2E2A38720084BFCC /* ConfigManager.h */,
				BECB709E2E2A38720084BFCC /* ConfigManager.m */,
				BECB709F2E2A38720084BFCC /* GlobalDataManager.h */,
				BECB70A02E2A38720084BFCC /* GlobalDataManager.m */,
				BECB70A12E2A38720084BFCC /* InitializationManager.h */,
				BECB70A22E2A38720084BFCC /* InitializationManager.m */,
				BECB70A32E2A38720084BFCC /* PaymentManager.h */,
				BECB70A42E2A38720084BFCC /* PaymentManager.m */,
				BECB70A52E2A38720084BFCC /* RiskControlManager.h */,
				BECB70A62E2A38720084BFCC /* RiskControlManager.m */,
				BECB70A72E2A38720084BFCC /* ThirdPartySDKManager.h */,
				BECB70A82E2A38720084BFCC /* ThirdPartySDKManager.m */,
			);
			path = Business;
			sourceTree = "<group>";
		};
		BECB70B42E2A38720084BFCC /* Core */ = {
			isa = PBXGroup;
			children = (
				BECB70AA2E2A38720084BFCC /* Constants.h */,
				BECB70AB2E2A38720084BFCC /* Constants.m */,
				BECB70AC2E2A38720084BFCC /* CryptoUtils.h */,
				BECB70AD2E2A38720084BFCC /* CryptoUtils.m */,
				BECB70AE2E2A38720084BFCC /* DeviceInfoManager.h */,
				BECB70AF2E2A38720084BFCC /* DeviceInfoManager.m */,
				BECB70B02E2A38720084BFCC /* KeychainManager.h */,
				BECB70B12E2A38720084BFCC /* KeychainManager.m */,
				BECB70B22E2A38720084BFCC /* UserDefaultsManager.h */,
				BECB70B32E2A38720084BFCC /* UserDefaultsManager.m */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		BECB70B62E2A38720084BFCC /* Example */ = {
			isa = PBXGroup;
			children = (
				B2BF46180EDA4518A167B4CB /* H5SDKExample.h */,
				BECB70B52E2A38720084BFCC /* H5SDKExample.m */,
			);
			path = Example;
			sourceTree = "<group>";
		};
		BECB70C42E2A38720084BFCC /* Network */ = {
			isa = PBXGroup;
			children = (
				BECB71042E2A38720084BFCC /* NetworkManager.h */,
				BECB71052E2A38720084BFCC /* NetworkManager.m */,
				BECB71062E2A38720084BFCC /* APIClient.h */,
				BECB71072E2A38720084BFCC /* APIClient.m */,
				BECB70BA2E2A38720084BFCC /* CacheService.h */,
				BECB70BB2E2A38720084BFCC /* CacheService.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		BECB70C92E2A38720084BFCC /* SDK */ = {
			isa = PBXGroup;
			children = (
				BECB70C52E2A38720084BFCC /* H5SDK.h */,
				BECB70C62E2A38720084BFCC /* H5SDK.m */,
				BECB70C72E2A38720084BFCC /* SDKConfig.h */,
				BECB70C82E2A38720084BFCC /* SDKConfig.m */,
			);
			path = SDK;
			sourceTree = "<group>";
		};
		BECB70CF2E2A38720084BFCC /* UI */ = {
			isa = PBXGroup;
			children = (
				BECB70CB2E2A38720084BFCC /* LoginViewController.h */,
				BECB70CC2E2A38720084BFCC /* LoginViewController.m */,
				BECB70CD2E2A38720084BFCC /* WebViewController.h */,
				BECB70CE2E2A38720084BFCC /* WebViewController.m */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		BECB70D42E2A38720084BFCC /* Utils */ = {
			isa = PBXGroup;
			children = (
				BECB70D02E2A38720084BFCC /* ErrorFactory.h */,
				BECB70D12E2A38720084BFCC /* ErrorFactory.m */,
				BECB70D22E2A38720084BFCC /* Logger.h */,
				BECB70D32E2A38720084BFCC /* Logger.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		BECB70D52E2A38720084BFCC /* H5SDK */ = {
			isa = PBXGroup;
			children = (
				BECB70A92E2A38720084BFCC /* Business */,
				BECB70B42E2A38720084BFCC /* Core */,
				BECB70B62E2A38720084BFCC /* Example */,
				BECB70C42E2A38720084BFCC /* Network */,
				BECB70C92E2A38720084BFCC /* SDK */,
				BECB70CF2E2A38720084BFCC /* UI */,
				BECB70D42E2A38720084BFCC /* Utils */,
			);
			path = H5SDK;
			sourceTree = "<group>";
		};
		BECB70E32E2A38720084BFCC /* O6 */ = {
			isa = PBXGroup;
			children = (
				BECB70D52E2A38720084BFCC /* H5SDK */,
				BECB70D62E2A38720084BFCC /* AppDelegate.h */,
				BECB70D72E2A38720084BFCC /* AppDelegate.m */,
				BECB70D82E2A38720084BFCC /* Assets.xcassets */,
				BECB70D92E2A38720084BFCC /* Info.plist */,
				BECB70DB2E2A38720084BFCC /* LaunchScreen.storyboard */,
				BECB70DC2E2A38720084BFCC /* main.m */,
				BECB70DE2E2A38720084BFCC /* Main.storyboard */,
				BECB70DF2E2A38720084BFCC /* SceneDelegate.h */,
				BECB70E02E2A38720084BFCC /* SceneDelegate.m */,
				BECB70E12E2A38720084BFCC /* ViewController.h */,
				BECB70E22E2A38720084BFCC /* ViewController.m */,
			);
			path = O6;
			sourceTree = "<group>";
		};
		BEE39CA12E250630004F556A = {
			isa = PBXGroup;
			children = (
				BECB70E32E2A38720084BFCC /* O6 */,
				BEE39CAB2E250630004F556A /* Products */,
				C9C60F9E8440018A75E72684 /* Pods */,
				2CA9E7FE3C8077B3F6909239 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		BEE39CAB2E250630004F556A /* Products */ = {
			isa = PBXGroup;
			children = (
				BEE39CAA2E250630004F556A /* O6.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C9C60F9E8440018A75E72684 /* Pods */ = {
			isa = PBXGroup;
			children = (
				50E85EE69B3242514622F824 /* Pods-O6.debug.xcconfig */,
				3B6C6F8E312468D2D8A42481 /* Pods-O6.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		BEE39CA92E250630004F556A /* O6 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BEE39CC22E250632004F556A /* Build configuration list for PBXNativeTarget "O6" */;
			buildPhases = (
				B62EE4BFF38DE7983282FE93 /* [CP] Check Pods Manifest.lock */,
				BEE39CA62E250630004F556A /* Sources */,
				BEE39CA72E250630004F556A /* Frameworks */,
				BEE39CA82E250630004F556A /* Resources */,
				8DD013F0D8321BB8BF56F368 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = O6;
			productName = O6;
			productReference = BEE39CAA2E250630004F556A /* O6.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BEE39CA22E250630004F556A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					BEE39CA92E250630004F556A = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = BEE39CA52E250630004F556A /* Build configuration list for PBXProject "O6" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = BEE39CA12E250630004F556A;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = BEE39CAB2E250630004F556A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				BEE39CA92E250630004F556A /* O6 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		BEE39CA82E250630004F556A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BECB71002E2A38720084BFCC /* Assets.xcassets in Resources */,
				BECB71022E2A38720084BFCC /* LaunchScreen.storyboard in Resources */,
				BECB71032E2A38720084BFCC /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		8DD013F0D8321BB8BF56F368 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-O6/Pods-O6-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-O6/Pods-O6-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-O6/Pods-O6-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B62EE4BFF38DE7983282FE93 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-O6-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		BEE39CA62E250630004F556A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BECB70E42E2A38720084BFCC /* ConfigManager.m in Sources */,
				BECB70E52E2A38720084BFCC /* GlobalDataManager.m in Sources */,
				BECB70E62E2A38720084BFCC /* InitializationManager.m in Sources */,
				BECB70E72E2A38720084BFCC /* PaymentManager.m in Sources */,
				BECB70E82E2A38720084BFCC /* RiskControlManager.m in Sources */,
				BECB70E92E2A38720084BFCC /* ThirdPartySDKManager.m in Sources */,
				BECB70EA2E2A38720084BFCC /* Constants.m in Sources */,
				BECB70EB2E2A38720084BFCC /* CryptoUtils.m in Sources */,
				BECB70EC2E2A38720084BFCC /* DeviceInfoManager.m in Sources */,
				BECB70ED2E2A38720084BFCC /* KeychainManager.m in Sources */,
				BECB70EE2E2A38720084BFCC /* UserDefaultsManager.m in Sources */,
				BECB70EF2E2A38720084BFCC /* H5SDKExample.m in Sources */,
				BECB71092E2A38720084BFCC /* NetworkManager.m in Sources */,
				BECB710A2E2A38720084BFCC /* APIClient.m in Sources */,
				BECB70F12E2A38720084BFCC /* CacheService.m in Sources */,
				BECB70F62E2A38720084BFCC /* H5SDK.m in Sources */,
				BECB70F72E2A38720084BFCC /* SDKConfig.m in Sources */,
				BECB70F82E2A38720084BFCC /* LoginViewController.m in Sources */,
				BECB70F92E2A38720084BFCC /* WebViewController.m in Sources */,
				BECB70FA2E2A38720084BFCC /* ErrorFactory.m in Sources */,
				BECB70FB2E2A38720084BFCC /* Logger.m in Sources */,
				BECB70FC2E2A38720084BFCC /* AppDelegate.m in Sources */,
				BECB70FD2E2A38720084BFCC /* main.m in Sources */,
				BECB70FE2E2A38720084BFCC /* SceneDelegate.m in Sources */,
				BECB70FF2E2A38720084BFCC /* ViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		BECB70DB2E2A38720084BFCC /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BECB70DA2E2A38720084BFCC /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		BECB70DE2E2A38720084BFCC /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				BECB70DD2E2A38720084BFCC /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		BEE39CC32E250632004F556A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 50E85EE69B3242514622F824 /* Pods-O6.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 764FX73K48;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = O6/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "Camera use";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Microphone use";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "Test Tracking";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.test.demok2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = test2;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		BEE39CC42E250632004F556A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B6C6F8E312468D2D8A42481 /* Pods-O6.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 764FX73K48;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = O6/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "Camera use";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Microphone use";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "Test Tracking";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.test.demok2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = test2;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		BEE39CC52E250632004F556A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		BEE39CC62E250632004F556A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		BEE39CA52E250630004F556A /* Build configuration list for PBXProject "O6" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BEE39CC52E250632004F556A /* Debug */,
				BEE39CC62E250632004F556A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BEE39CC22E250632004F556A /* Build configuration list for PBXNativeTarget "O6" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BEE39CC32E250632004F556A /* Debug */,
				BEE39CC42E250632004F556A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BEE39CA22E250630004F556A /* Project object */;
}
