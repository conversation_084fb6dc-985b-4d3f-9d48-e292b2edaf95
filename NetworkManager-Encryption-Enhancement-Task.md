# Context
Filename: NetworkManager-Encryption-Enhancement-Task.md
Created On: 2025-01-29
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
对NetworkManager.m进行加密增强改造：
1. 只保留POST请求，去掉GET请求
2. 网络请求添加加密拓展：
   - 将网络请求头headers的参数加入parameters中，使用字段"http_headers"
   - 加密POST请求的parameters，先将parameters转为json字符串，然后加密为加密字符串，转为data，request的httpBody改为使用加密后的data
   - 解密接口返回的data数据，解密时需要对加密文本做特殊处理（去除\r\n和空白字符）
   - 不需要添加加解密开关属性
   - 加解密key说明：
     - 对于getAppConfig接口，使用baseURL的host作为key
     - 对于其他接口，从appConfigData中使用getAppConfigString方法获取字段名为encrypt_key的值作为key

# Project Overview
H5SDK项目，包含完整的网络层、加密工具、配置管理等模块。当前NetworkManager支持GET和POST请求，需要改造为仅支持POST请求并添加加密功能。

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## 现有代码结构分析

### NetworkManager当前实现
- 位置：O6/H5SDK/Network/NetworkManager.m
- 支持GET和POST请求方法
- 包含JSON响应处理
- 使用NSURLSession进行网络请求
- 当前POST请求将parameters转为JSON并设置到httpBody

### 加密工具类
- 位置：O6/H5SDK/Core/CryptoUtils.h/m
- 提供AESEncryptionStrategy和Base64EncryptionStrategy
- CryptoUtils单例类提供便捷的加解密方法
- 已有encryptRiskControlData方法可参考

### 配置管理
- GlobalDataManager提供getAppConfigString方法获取encrypt_key
- Constants定义baseURL为"https://test-app.lurve.cc"
- getAppConfig接口路径为"/config/getAppConfigPostV2"

### 关键依赖关系
- NetworkManager需要导入CryptoUtils.h
- 需要导入GlobalDataManager.h获取配置数据
- 需要导入Constants.h获取baseURL信息

## 技术约束
- 必须保持现有API接口不变
- 需要处理加密失败的错误情况
- 解密时需要特殊处理字符串格式
- 需要区分getAppConfig接口和其他接口的加密key获取方式

## 实现要点
1. 移除所有GET请求相关方法和实现
2. 修改POST请求实现，添加加密逻辑
3. 修改响应处理，添加解密逻辑
4. 添加必要的依赖导入
5. 处理加密key的获取逻辑
6. 添加错误处理机制

# Proposed Solution (Populated by INNOVATE mode)

## 解决方案设计

### 方案1：最小侵入式改造
- 保持现有方法签名不变
- 在buildPOSTRequest方法中添加加密逻辑
- 在handleJSONResponse方法中添加解密逻辑
- 优点：改动最小，兼容性好
- 缺点：逻辑相对分散

### 方案2：重构式改造
- 新增专门的加密请求构建方法
- 新增专门的解密响应处理方法
- 重新组织代码结构
- 优点：代码结构清晰，易于维护
- 缺点：改动较大

### 推荐方案：混合式改造
结合两种方案的优点：
1. 保持现有公共接口不变
2. 新增私有的加密相关辅助方法
3. 在现有方法中集成加密逻辑
4. 清晰的错误处理和日志记录

### 加密key获取策略
- 通过URL判断是否为getAppConfig接口
- getAppConfig接口：解析baseURL获取host作为key
- 其他接口：通过GlobalDataManager获取encrypt_key

### 错误处理策略
- 加密失败时返回明确的错误信息
- 解密失败时返回原始数据或错误
- 添加适当的日志记录便于调试

# Implementation Plan (Generated by PLAN mode)

## 详细实施计划

### 文件修改清单
- NetworkManager.h：移除GET请求方法声明
- NetworkManager.m：实现加密增强逻辑

### 具体修改步骤

#### 步骤1：添加必要的导入
在NetworkManager.m顶部添加：
```objc
#import "CryptoUtils.h"
#import "GlobalDataManager.h"
#import "Constants.h"
```

#### 步骤2：移除GET请求相关代码
- 移除GET方法实现（第94-113行，第140-148行）
- 移除buildGETRequest方法（第162-194行）
- 移除buildQueryString和URLEncode方法（第232-245行）

#### 步骤3：添加加密相关私有方法
- 添加获取加密key的方法
- 添加参数加密方法
- 添加响应解密方法

#### 步骤4：修改POST请求实现
- 修改buildPOSTRequest方法，集成加密逻辑
- 将headers合并到parameters中
- 对合并后的parameters进行加密

#### 步骤5：修改响应处理
- 修改handleJSONResponse方法
- 添加解密逻辑
- 处理解密后的数据

#### 步骤6：更新头文件
- 从NetworkManager.h中移除GET请求方法声明

## Implementation Checklist:
1. 在NetworkManager.m中添加必要的导入语句
2. 从NetworkManager.h中移除GET请求方法声明
3. 从NetworkManager.m中移除GET请求方法实现
4. 从NetworkManager.m中移除buildGETRequest方法
5. 从NetworkManager.m中移除buildQueryString和URLEncode辅助方法
6. 添加获取加密key的私有方法getEncryptionKeyForURL
7. 添加参数加密的私有方法encryptParameters
8. 添加响应解密的私有方法decryptResponseData
9. 修改buildPOSTRequest方法，集成headers合并和参数加密逻辑
10. 修改handleJSONResponse方法，添加响应解密逻辑
11. 添加适当的错误处理和日志记录

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "完成所有实施步骤"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   [2025-01-29 14:30]
    *   Step: 步骤1-6 - 清理现有代码和添加导入
    *   Modifications:
        - NetworkManager.m: 添加了CryptoUtils.h、GlobalDataManager.h、Constants.h导入
        - NetworkManager.h: 移除了GET请求方法声明（第29-33行，第37-41行）
        - NetworkManager.m: 移除了GET方法实现（第97-118行，第122-130行）
        - NetworkManager.m: 移除了buildGETRequest方法（第144-176行）
        - NetworkManager.m: 移除了buildQueryString方法（第214-223行）
        - NetworkManager.m: 移除了URLEncode方法（第225-227行）
    *   Change Summary: 完成了代码清理工作，移除了所有GET请求相关代码，添加了必要的导入语句
    *   Reason: 执行计划步骤1-6，为后续加密功能实现做准备
    *   Blockers: None
    *   User Confirmation Status: [Success]
*   [2025-01-29 14:45]
    *   Step: 步骤7-12 - 实现加密功能
    *   Modifications:
        - NetworkManager.m: 添加了getEncryptionKeyForURL私有方法，支持根据URL获取不同的加密key
        - NetworkManager.m: 添加了encryptParameters私有方法，实现参数和headers合并及AES加密
        - NetworkManager.m: 添加了decryptResponseData私有方法，实现响应数据解密和字符串清理
        - NetworkManager.m: 修改了buildPOSTRequest方法，集成加密逻辑，使用application/octet-stream内容类型
        - NetworkManager.m: 修改了handleJSONResponse方法，添加forURL参数并集成解密逻辑
        - NetworkManager.m: 修改了POST JSONCompletion方法，传递URLString参数给handleJSONResponse
    *   Change Summary: 完成了完整的加密增强功能实现，包括参数加密、响应解密、key管理等核心功能
    *   Reason: 执行计划步骤7-12，实现网络请求的端到端加密功能
    *   Blockers: None
    *   Status: [Pending Confirmation]
*   [2025-01-29 14:50]
    *   Step: 额外修改 - 优化加密key处理和Content-Type设置
    *   Modifications:
        - CryptoUtils.m: 修改keyDataFromString方法，改为字符串补"0"字符的方式，而不是字节补0
        - NetworkManager.m: 用户手动将Content-Type改回"application/json"（从"application/octet-stream"）
        - NetworkManager.m: 用户手动移除了注释行
    *   Change Summary: 优化了加密key的生成方式，使用字符串补0而不是字节补0，保持Content-Type为application/json
    *   Reason: 根据用户反馈优化实现细节
    *   Blockers: None
    *   Status: [Pending Confirmation]
*   [2025-01-29 15:00]
    *   Step: 额外功能 - 添加AppConfig特殊解密逻辑
    *   Modifications:
        - NetworkManager.m: 添加了decryptAppConfigData私有方法，实现AppConfig数据的特殊解密逻辑
        - NetworkManager.m: 修改了handleJSONResponse方法，对AppConfig接口返回的数据进行特殊解密处理
        - 解密逻辑：使用Constants的key2、key3、key4获取加密数据，将第2、3位base64解码后合并作为解密key，对第4位数据进行AES解密
        - APIClient.m: 撤销了之前在APIClient中添加的解密逻辑，保持架构清晰
    *   Change Summary: 在NetworkManager中实现了AppConfig接口的特殊解密功能，符合职责分离原则
    *   Reason: 根据用户建议，将解密逻辑放在NetworkManager中更合适，保持架构清晰
    *   Blockers: None
    *   Status: [Pending Confirmation]

# Final Review (Populated by REVIEW mode)
