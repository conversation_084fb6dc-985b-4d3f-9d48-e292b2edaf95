## 1. 网络
 1.1 使用 await/async
 1.2 统一网络请求header参数，定义header的格式，顺序，方式做修改, 以下代码作为取值参考。
 ```swift
        var headers = [
            "sec_ver": "0",
            "platform": "iOS",
            "is_anchor": "false"
        ]  

        // Add device and app information headers
        headers["ver"] = deviceInfo.appVersion
        headers["device-id"] = deviceInfo.deviceId
        headers["model"] = deviceInfo.deviceModel
        headers["lang"] = deviceInfo.languageCode
        headers["sys_lan"] = deviceInfo.systemLanguage
        headers["platform_ver"] = deviceInfo.systemVersion
        headers["pkg"] = deviceInfo.bundleIdentifier
        headers["device_lang"] = deviceInfo.systemLanguage
        headers["device_country"] = deviceInfo.countryCode
        headers["time_zone"] = deviceInfo.timeZone

        // Add authorization token if available
        if let token = GlobalDataManager.shared.authToken {
            headers["Authorization"] = "Bearer \(token)"
        }

        if let attribution = trackManager.getAttribution() {
            headers.merge(attribution) { (current, _) in current }
        }

        return headers
 ```

 1.3 网络返回结构：
  {"code":0,"key":"common_success","msg":"成功","data":T,"success":true,"fail":false}

  结构说明：data字段包含实际需要用到的业务数据（类型不定，可能是 布尔，字典，数组等），success为 true 时，表示业务逻辑成功，fail为 true 时，表示业务逻辑错误。

 1.4 先实现以下接口，不创建模型，只返回 data 中的数据。
 ```json
   1. 应用配置接口：
   接口地址：'/config/getAppConfig'
   请求参数：
   返回结果：{"code":0,"success":true,"data":{"items":[{"name":"glt","data":"AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"rck","data":"qf3d5gbjq92th"},{"name":"rc_app_key","data":"lmxuhwagl7lid"},{"name":"rc_area_code","data":"SG"},{"name":"rc_navi_server","data":"nav.sg-light-edge.com"},{"name":"rc_stat_server","data":"stats.sg-light-edge.com"},{"name":"rtck","data":"e72270b7a0b4463989252f55e68ff731"},{"name":"zego_app_id","data":396347616},{"name":"simple-performance-network","data":"0.1"},{"name":"log_cdn_performance","data":"true"},{"name":"tpp_ua","data":"0"},{"name":"im_greeting","data":"0"},{"name":"anchor_mask","data":"0"},{"name":"user_behavior_submit_enable","data":"1"},{"name":"mg_broadcaster_call_enable_max_seconds","data":3600},{"name":"encrypt_key","data":"f919268214fa4ed7a0e9fcb2136d1ba0"},{"name":"is_encrypt","data":"true"},{"name":"is_open_invitation","data":"false"},{"name":"tpp_open_type","data":"1"},{"name":"app_ext_data","data":{"h5_subpath":"\/h5_web\/v6\/","flash_noti_enabled":"1","flash_time":"3600","banner_enabled":"1","flash_times":"[240,60,60,60]","flash_wait_content":"Wait for free tomorrow.\nOr recharge to match now!","app_dominate":"0","app_dominate_count":"5","banners":"[{\"cover_url\":\"2023-05-17\/h5banner\/youxi.png\",\"jump_url\":\"http:\/\/game.abv.cn\/00lobby\/?sign={sign}&ext=hall\",\"type\":1}]","loading_img_url":"https:\/\/test-h5.snoperp.com\/h5_web\/v5\/assets\/assets\/audio\/login_video.jpeg","explore_tab":"[{\"title\":\"Discover\",\"type\":\"discover\"}]","flash_noti_content":"You've got a free match. Video Chat with global girls now!","app_screenshot_enabled":"1","flash_noti_title":"Free Video Chat with Girls!","app_screenshot_num":"7","app_input_check_enabled":"0","app_sub_dominate":"0","safety":"EWpv1a4z6bP3WKwbRtpdmRGmPNk7RrfbpmGQn8x1kcSxBYvpWimQqCaoIH5zZ\/h5C9uE8rsRz\/JZ\/sjjBOvsB8Bn\/ijfkg1EYY+MSWygZb1r0Prr0Ho6VtoeP7FlSa\/kcQ2E+ZoNchTcLQfA4LMuPBsUw7\/HWuwc2DVXarbDkWM=","h5_fullpath":"http:\/\/test-h5.snoperp.com\/h5_web\/v6\/","flash_no_perrsion":"Allow notifications to get free match alerts. Go to Settings and turn them on now!"}},{"name":"translate_v2","data":"https:\/\/translation.googleapis.com\/language\/translate\/v2?key=AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"google_translation_key","data":"AIzaSyCY4MncZdTNDX22qyoXBCndAsQALC0gFtU"},{"name":"microsoft_translation_key","data":""},{"name":"translate_type","data":2},{"name":"ios_app_id","data":"6746118609"},{"name":"is_enable_email_login","data":"false"},{"name":"anchor_netspeedcheck_enable","data":"1"},{"name":"hack_revenue_factor","data":0.29999999999999999},{"name":"attribution_sdk","data":"AF"},{"name":"anchor_netspeedcheck","data":{"url":"https:\/\/dldir1.qq.com\/weixin\/Windows\/WeChatSetup.exe","sizeLimit":30,"interval":172800000}},{"name":"time_log_upload_num","data":"1000"},{"name":"time_log_upload_backup","data":"0"},{"name":"anchor_beauty_min_frame","data":0},{"name":"anchor_beauty_max_cost","data":200},{"name":"anchor_beauty_min_time","data":20},{"name":"pgstts","data":1}],"rvsta":"2","ver":"6","riskControlInfoConfig":{"k_interval":5,"k_factor_num":"TJA160","k_factor":"c8ehl28i8nowbuai"}},"fail":false,"key":"common_success","msg":"成功"}
   
   2. 登录接口：
   接口地址：'/security/oauth'
   请求参数：["oauthType": 4, "token": deviceId]
   返回结果：{"code":0,"data":{"aaaa":0,"isFirstRegister":false,"token":"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTIyMTUwMTk4NDg2ODkyNTQ0IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDI4ODcxNTcsImNyZWF0ZWQiOjE3NDcyMTM1NTc4Mzd9.f6PD4dWe4KB19fuaQ6Zgbn9c2GdXQcNYKJ4v6SI-J88mTxpA4_2hS81y5KGkeDrL3IDSg6MZwnE9wtV4zarMfA","userInfo":{"age":30,"auditStatus":1,"availableCoins":0,"avatar":"20250226/******************************.png","avatarMiddleThumbUrl":"https://test-agilecdn.livchat.me/20250226/******************************.png?Expires=1754383254&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=mU5mwkYm%2F4zNLPf2n5KD5D0REXM%3D&x-oss-process=image%2Fcrop%2Cx_20%2Cy_0%2Cw_120%2Ch_160%2Fresize%2Cm_fill%2Ch_480%2Cw_360%2Climit_1","avatarStatus":0,"avatarThumbUrl":"https://test-agilecdn.livchat.me/20250226/******************************.png?Expires=1754383254&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=SMlNBflqBeDsJXfY68PKtfNNyCY%3D&x-oss-process=image%2Fcrop%2Cx_20%2Cy_0%2Cw_120%2Ch_160%2Fresize%2Cm_fill%2Ch_160%2Cw_120%2Climit_1","avatarUrl":"https://test-agilecdn.livchat.me/20250226/******************************.png?Expires=1754383254&OSSAccessKeyId=LTAI5tNPNT6g1inCHkxgcUGz&Signature=TJ%2B4sNfmYRrOUlXTUcZcJh9sVzE%3D&x-oss-process=image%2Fcrop%2Cx_20%2Cy_0%2Cw_120%2Ch_160%2Fresize%2Cm_fill%2Ch_1080%2Cw_810%2Climit_1","birthday":"1995-01-01","broadcasterType":0,"country":"USA","createTime":1747111293744,"followNum":0,"gender":1,"giftWallAction":0,"hasEquity":false,"hasIllegalAvatar":0,"isAnswer":true,"isBlock":false,"isFakeBroadcaster":false,"isGreenMode":false,"isHavePassword":false,"isInternal":false,"isMultiple":false,"isRecharge":false,"isReview":false,"isSwitchNotDisturbCall":false,"isSwitchNotDisturbIm":false,"isVip":false,"level":0,"loginPkgName":"com.buzzhum.ios","mysteriousInfo":{"effective":false},"nickname":"Mike","praiseNum":0,"rcToken":"<EMAIL>;5n1t.sg.rongcfg.com","registerCountry":"SG","registerPkgName":"com.buzzhum.ios","rongcloudToken":"<EMAIL>;hw3r.cn.rongcfg.com","tagDetails":[{"tag":"NEW","tagColor":"#24c45d","tagTip":"Attention: If the call duration is < 40s, you'll not get coins."},{"tag":"Coinless","tagColor":"#999999","tagTip":"Send him a recharge link with discount after the call"}],"tagsList":["New"],"userId":"1922150198486892544","userType":1}},"fail":false,"key":"common_success","msg":"登录成功","success":true}
   
   3. 策略接口(避免使用Strategy敏感词来定义接口地址，方法名称等。)
   接口地址： 'config/getStrategy'
   请求参数：无
   返回结果：{"code":0,"key":"common_success","msg":"成功","data":{"isMatchCallFree":true,"initTab":0,"isShowMatchGender":true,"genderMatchCoin":{"maleCoins":9,"femaleCoins":9,"bothCoins":0,"vipGoddessCoins":25,"goddessCoins":30},"isReviewPkg":false,"isShowLP":false,"lpDiscount":0,"lpPromotionDiscount":0,"payChannels":["IAP"],"isMaskOpen":true,"isShowBroadcasterRank":true,"payScriptUserCoins":40,"payScriptTriggerSecond":45,"fakeBroadcasterPopupSecond":30,"isAutoAccept":false,"showMeet":false,"broadcasterWallTags":["All"],"liveWallTagList":[],"tabType":1,"isOpenBroacasterInvitation":true,"isOpenFlashChat":false,"videoStreamCategory":["Live","Hot"],"flashChatConfig":{"isSwitch":true,"isFreeCall":false,"residueFreeCallTimes":0},"isShowMatch":false,"isNewTppUsable":true,"userInvitation":{"tipsTitle":"SHARE & EARN 100 COINS","tipsContent":"Share with a friend and earn 100 coins for free","popUpTitle":"Share with a friend and earn 100 coins for free","popUpContent":"Invite your friends to {packageName} and earn up to 100 free coins for each invited friend.","popUpBottom":"You'll earn 0 coins when your friend complete registration.\nYou'll earn 100 coins when your friend complete first purchase.","shareContent":"Join {packageName} and input my invitation code {invitationCode} to get 20 free coins! Enjoy live video chat at {invitationUrl}"},"topOfficialUserIds":["1349987244005523456","1291682940983574528","1351074033617207296","1916383162531971072"],"reviewOfficialBlacklistUserIds":["1302892211775602688","1499216468028555264","1464076471248224256","1349987244005523456","1291682940983574528","1351074033617207296","1295279702818291712","1349925887432327168","1334126717501046784","1351101555734085632","1285880626939035648","1529110821915983872"],"officialBlacklistUserIds":["1347831975964180480","1304351073493975040","1322454720689864704","1325722360338317312","1325729213021552640","1349925887432327168"],"imIncentiveBlacklistUserIds":["128329832844820300"],"broadcasterFollowOfficialUserIds":["1744612431927312384","1351101555734085632","1433684162828697600","1291682940983574528"],"isDisplayNotDisturbCall":true,"isDisplayNotDisturbIm":true,"imSessionBalance":3,"isShowFlowInfo":true,"isShowDeletedButton":true,"isShowLiveTab":false,"minLiveUserLevel":1,"broadcasterWallTagList":[{"tagName":"Popular","subTagList":["All","English","Asian","Europe","Latin","India"],"showTagName":"Popular","subTagInitIndex":0},{"tagName":"New","subTagList":["All"],"showTagName":"New","subTagInitIndex":0},{"tagName":"Followed","subTagList":["All"],"showTagName":"Followed","subTagInitIndex":0}],"freeUserCallStaySecond":"20","freeUserImStaySecond":"5","rechargeUserCallStaySecond":"20","rechargeUserImStaySecond":"10","isRandomUploadPaidEvents":false,"isSwitchIMLimit":true,"isSwitchOneKeyFollow":true,"isSwitchIMIncentive":false,"isSwitchClub":false,"isShowRookieGuide":false,"isSwitchStrongGuide":true,"isCallRearCamera":true,"isCallCameraClose":true,"isShowAutoTranslate":true,"isSilence":false,"isRearCamera":false,"isCloseCamera":false,"isSwitchInstruct":false,"isForceEvaluationInstruct":false,"isSwitchExtraCategory":false,"isSwitchMultipleCall":false,"timestamp":"*************","sayHiMaxCount":8,"sayHiQuickPhrases":["Hello sexy girl~","Show me baby,Baby, can you send the video?","You look so hot\uD83D\uDD25"],"userServiceAccountId":"1351074033617207296","broadcasterWallRegions":["CO","MA","BR","VE","VN","UA","PH","IN","TH","PE","EC","TR","AR","AZ","US","RU","ID"],"userMultipleLevel":4,"isReportFB":true,"isEnableGuardian":false,"isEnableCallCard":false,"isOpenSpeechToText":false,"voiceToTextConfig":{"voiceToTextSwitch":false,"voiceToTextUnitPrice":5},"isEnableGroupSend":true,"supportShowRoom":false,"newRelationMsgSizeLimit":5,"unansweredGreetingExpireTTLHour":1,"isOpenFlashChatOnRole":false,"broadcasterOnlineButton":"video","indiaWallCallButtonUI":"newUI","indiaWallLowCallUI":"oldLowCallUI","indiaRecommendShow":"new","indianWallUnlock":"unlock_india","callingSeconds":90,"isGuideInMode":false,"appPraiseSwitch":true,"liveComboLimitTime":200,"hideCountryFromLive":false,"highLightUnreadOfficialUserIds":["1744612431927312384"],"videoCallConfig":{"callNoticeContent":[["Hi,love~","Hi,baby","Hi,what are you doing","Hi,can we talk about something?","Hi,how are you handsome~","Wow,hi"],["Are you new here?","What can I do for you love","Do you wanna to see me something?"]],"firstSendDelaySecond":3,"sendDelayMinSecond":4,"sendDelayMaxSecond":8},"liveLevelEquityH5Url":"http://test-h5.isugar.xyz/userLevelbenefits/index.html?pkgName=com.buzzhum.ios&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTIyMTUwMTk4NDg2ODkyNTQ0IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDI4ODcxNTcsImNyZWF0ZWQiOjE3NDcyMTM1NTc4Mzd9.f6PD4dWe4KB19fuaQ6Zgbn9c2GdXQcNYKJ4v6SI-J88mTxpA4_2hS81y5KGkeDrL3IDSg6MZwnE9wtV4zarMfA","activityListH5Url":"http://test-h5.isugar.xyz/eventNotice/index.html?pkgName=com.buzzhum.ios&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTIyMTUwMTk4NDg2ODkyNTQ0IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDI4ODcxNTcsImNyZWF0ZWQiOjE3NDcyMTM1NTc4Mzd9.f6PD4dWe4KB19fuaQ6Zgbn9c2GdXQcNYKJ4v6SI-J88mTxpA4_2hS81y5KGkeDrL3IDSg6MZwnE9wtV4zarMfA","userSvipEquityH5Url":"http://test-h5.isugar.xyz/svipDetail/index.html?pkgName=com.buzzhum.ios&token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxOTIyMTUwMTk4NDg2ODkyNTQ0IiwidXNlcl90eXBlIjoxLCJleHAiOjQ5MDI4ODcxNTcsImNyZWF0ZWQiOjE3NDcyMTM1NTc4Mzd9.f6PD4dWe4KB19fuaQ6Zgbn9c2GdXQcNYKJ4v6SI-J88mTxpA4_2hS81y5KGkeDrL3IDSg6MZwnE9wtV4zarMfA","isShowWeeklyCard":false,"multiGuestConfig":{},"isSwitchSubscription":true,"openLiveUnderReview":true,"h5TaskUrl":"http://test-h5.isugar.xyz/h5HostessTask/index.html","isCR":false,"userLiveCountryTagConfigList":[],"isNo1V1":false,"comboCountdown":5,"popByGroupCoins":30000,"globalNoticeChatRoomConfig":{"broadcasterChatRoom":{"roomNo":"broadcaster_global_notification_room"},"userChatRoom":{"roomNo":"user_global_notification_room"}},"imSessionBroadcasterIds":[]},"success":true,"fail":false}
   
    4. 创建订单接口
    接口地址：'/coin/recharge/create'
    请求参数: goodsCode: "438700", payChannel: "IAP", source: ""
    返回结果：{"code":0,"key":"common_success","msg":"成功","data":{"goodsCode":"438700","goodsName":"100 Coins","orderNo":"ea39ab825a1f490e9857ab770f119c36","payAmount":0.99,"paidAmount":0.99,"paidCurrency":"USD"},"success":true,"fail":false}
   
    5. 消费IPA接口
    接口地址：'/coin/recharge/payment/ipa'
    请求参数：parameters: ["orderNo": orderNo, "payload": receipt, "transactionId": transactionId, "type": 1]
    返回结果：{"code":0,"key":"common_success","msg":"成功","data":true,"success":true,"fail":false}
   
    6. 归因上报接口
    接口地址：'/hit/ascribeRecordReqs'
    请求参数：parameter: 
    "utmSource": attribution.network?.urlEncoded ?? "",
    "adgroupId": attribution.adgroup?.urlEncoded ?? "",
    "adsetId": attribution.creative?.urlEncoded ?? "",
    "campaignId": attribution.campaign?.urlEncoded ?? "",
    "attributionSdk": "AJ",
    "attributionSdkVer": Adjust.sdkVersion() ?? "",
    "device-id": deviceInfo.deviceId,
    "ver": deviceInfo.appVersion,
    "pkg": deviceInfo.bundleId,
    "userId": userId ?? "",

    返回结果：{"code":0,"key":"common_success","msg":"成功","data":true,"success":true,"fail":false}
   
    7. 风控上报接口
    接口地址：'/risk/info/upload'
    请求参数：info: "" //加密后的风控数据
    返回结果：{"code":0,"key":"common_success","msg":"成功","data":true,"success":true,"fail":false}
   
    8. 获取状态接口
    接口地址：'/config/sh'
    请求参数：无
    返回结果：{"code":0,"key":"common_success","msg":"成功","data":{"sh":true,"interval":3,"remain_loop":10},"success":true,"fail":false}
```
   
## 2. 不要创建模型，不使用模型
 2.1 不要创建模型，直接解析接口返回的JSON数据，返回data中的数据。
 2.2 不要使用 Codable，直接使用 SwiftyJSON 解析数据。

## 3. 内购
 3.1 实现 Apple iOS 内购的功能，封装成内购类，方便调用。
 3.2 需要实现异步返回支付结果的功能。
 3.3 提供方便调用支付的接口，传入商品code，异步等待返回支付结果。
 3.4 超时处理 ：当前实现没有处理请求可能卡住的情况。考虑添加一个超时机制，在一定时间后自动重置 isRequestInProgress 。
 3.5 状态恢复 ：如果应用在购买过程中被终止， isRequestInProgress 状态会丢失。在应用重启时，您可能需要检查是否有未完成的交易并相应地恢复状态。
 3.6 内购预期流程：查询商品->创建订单->发起购买->购买成功->消费IPA.

## 4. 入口类（配置，登录，策略）
 4.1 提供一个方法，该方法执行必要的网络接口请求。
 4.2 先异步初始化Adjust，Facebook，获取Adjust的归因信息
 4.3 然后等待设备网络连接，接着分别按顺序执行 AppConfig，auth和策略接口，后面的接口需要等待前面接口完成。
 4.4 AppConfig如果有缓存，则使用缓存数据，同时异步请求接口更新缓存数据。
 4.5 如果有Auth数据（即已登录，同时判断本地Token和Auth数据），则跳过登录。
 4.6 如果有策略数据缓存，则跳过策略接口，策略接口不异步刷新。
 4.7 获取策略数据后，从策略数据中获取字段“isReviewPkg”的值，如果为 true，则延迟 2 秒再获取一次策略数据，再次判断“isReviewPkg”的值，如果仍然为 true，则抛出错误。即使 isReviewPkg 为true，也需要保存策略数据
 4.8 针对策略数据缓存，直接判断isReviewPkg，如果为true，则直接抛出错误。
 4.8 如果获取AppConfig，Auth和策略数据失败（其中任意一项），则中断初始化流程，提示用户重试。

## 5. Adjust，Facebook
 5.1 集成 Adjust 和 Facebook 。
 5.2 使用Cocoapod
 5.3 使用一类来管理。
 5.4 Facebook的统计下单事件，购买事件。
 5.5 Adjust的统计购买事件，下单事件，登录事件，注册事件记录。
 5.6 登录接口中，如果isFirstRegister是true，则记录注册事件，否则记录登录事件。
 5.7 消费IPA接口成功后，记录购买事件，参数：paidAmount，paidCurrency
 5.8 下单接口成功后，记录下单事件，参数：paidAmount，paidCurrency

## 6. 网页
 6.1 创建一个 App 级别的网页控制器，适合用来承载一个 web app。
 6.2 网页需要在atDocumentStart时，通过注入用户脚本（WKUserScript）方式，向网页传递初始化参数。
 6.3 需要向wkwebview注册网页可以调用的原生事件（WKScriptMessageHandler），处理网页回调事件。
 6.4 需要实现比较多wkwebview自定义配置（WKWebViewConfiguration，wkwebview 属性）。
 6.5 顶部添加加载进度条.

## 7. 全局数据
 7.1 提供方便的数据访问和管理。
 7.2 管理初始化流程中的应用AppConfig数据，登录授权数据（AuthData），策略数据

## 8. Keychain
 8.1 生成和管理 device-id，使用UUID的方式生成。

## 9. UserDefault
 9.1 提供方便本地数据存取。
 9.2 只生成需要用到的方法。

## 10. 设备信息
 10.1 提供方便获取设备信息的方法
 ```
 appVersion
 deviceId
 deviceModel
 languageCode
 systemLanguage
 systemVersion
 bundleIdentifier
 systemLanguage
 countryCode
 timeZone
 ```

## 11. 类名，函数名，变量名，常量名等避免使用review，webview等关键词。

## 12. 代码生成原则：精简代码，不生成非必要的代码。

## 13. 根据Appconfig接口返回的数据结构，添加获取配置信息的便捷方法

## 14 风控数据定义与加密
 
 - 定义以下风控数据：
   ```swift
   "platform": "iOS",
   "pkg": bundleId,
   "ver": appVersion,
   "platform_ver": systemVersion,
   "model": deviceModel,
   "user_id": userId ?? "",
   "device_id": deviceId,
   "system_language": languageCode,
   "time_zone": timeZone,
   "is_enable_vpn": isVPNEnabled() ? 1 : 0,
   "is_enable_proxy": isProxyEnabled() ? 1 : 0,
   "is_jailbreaking": isJailbroken() ? 1 : 0,
   "is_emulator": isSimulator() ? 1 : 0
   ```
 - 风控数据加密函数：
   - 风控字典转换为jsonData，jsonData转换为jsonString
   - 使用加密工具类AES-ECB加密jsonString
   - 加密key：从appConfigData中获取riskControlInfoConfig，再从riskControlInfoConfig中获取字段为k_factor的值。

## 15. 登录页
- 我提供了设计图，请按照设计图开发页面。
- 初始化流程是进入登录页面就开始异步执行。
- Fast Login”按钮点击后需要先校验协议同意状态，点击后根据初始化结果判断跳转的页面。
- 如果点击登录按钮后，SDK正在初始化中，还未有结果，则显示loadingIndicator，隐藏登录按钮和协议栏，等待初始化结果做跳转。
